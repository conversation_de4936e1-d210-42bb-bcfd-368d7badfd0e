import os
import sqlite3
from pathlib import Path

def init_database():
    """初始化数据库，创建所有必要的表和索引"""
    # 获取项目根目录
    root_dir = Path(__file__).parent.parent
    db_path = root_dir / 'stock_data.db'
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 执行所有SQL文件
    sql_files = [
        'scripts/create_trades_table.sql',
        'scripts/add_technical_indicators.sql'
    ]
    
    for sql_file in sql_files:
        try:
            with open(root_dir / sql_file, 'r') as f:
                sql = f.read()
                cursor.executescript(sql)
                print(f"执行 {sql_file} 成功")
        except Exception as e:
            print(f"执行 {sql_file} 失败: {e}")
    
    # 提交更改
    conn.commit()
    conn.close()
    print("数据库初始化完成")

if __name__ == '__main__':
    init_database() 