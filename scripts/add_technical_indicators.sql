-- 添加均线相关字段
ALTER TABLE stock_history ADD COLUMN ma5 FLOAT;
ALTER TABLE stock_history ADD COLUMN ma10 FLOAT;
ALTER TABLE stock_history ADD COLUMN ma20 FLOAT;
ALTER TABLE stock_history ADD COLUMN ma30 FLOAT;
ALTER TABLE stock_history ADD COLUMN ma60 FLOAT;

-- 添加成交量均线字段
ALTER TABLE stock_history ADD COLUMN volume_ma5 FLOAT;
ALTER TABLE stock_history ADD COLUMN volume_ma10 FLOAT;
ALTER TABLE stock_history ADD COLUMN volume_ma20 FLOAT;

-- 添加振幅和波动率相关字段
ALTER TABLE stock_history ADD COLUMN amplitude_5d FLOAT;  -- 5日振幅
ALTER TABLE stock_history ADD COLUMN amplitude_10d FLOAT; -- 10日振幅
ALTER TABLE stock_history ADD COLUMN daily_volatility FLOAT; -- 日内波动率 (high-low)/close

-- 添加成交量比率字段
ALTER TABLE stock_history ADD COLUMN volume_ratio_5d FLOAT;  -- 当日成交量/5日平均值
ALTER TABLE stock_history ADD COLUMN volume_ratio_10d FLOAT; -- 当日成交量/10日平均值

-- 添加趋势指标字段
ALTER TABLE stock_history ADD COLUMN ma5_deviation FLOAT;   -- 收盘价相对MA5偏离率
ALTER TABLE stock_history ADD COLUMN ma10_deviation FLOAT;  -- 收盘价相对MA10偏离率
ALTER TABLE stock_history ADD COLUMN ma_trend FLOAT;        -- MA5相对MA10偏离率

-- 添加支撑压力相关指标
ALTER TABLE stock_history ADD COLUMN pressure_ma5 FLOAT;    -- 5日均线压力（当前价格低于MA5时有效）
ALTER TABLE stock_history ADD COLUMN pressure_ma10 FLOAT;   -- 10日均线压力
ALTER TABLE stock_history ADD COLUMN pressure_ma20 FLOAT;   -- 20日均线压力
ALTER TABLE stock_history ADD COLUMN support_ma5 FLOAT;     -- 5日均线支撑（当前价格高于MA5时有效）
ALTER TABLE stock_history ADD COLUMN support_ma10 FLOAT;    -- 10日均线支撑
ALTER TABLE stock_history ADD COLUMN support_ma20 FLOAT;    -- 20日均线支撑

-- 添加量价支撑压力指标
ALTER TABLE stock_history ADD COLUMN volume_price_pressure_5d FLOAT;  -- 5日量价压力（高于当前价格的成交量占比）
ALTER TABLE stock_history ADD COLUMN volume_price_pressure_10d FLOAT; -- 10日量价压力
ALTER TABLE stock_history ADD COLUMN volume_price_support_5d FLOAT;   -- 5日量价支撑（低于当前价格的成交量占比）
ALTER TABLE stock_history ADD COLUMN volume_price_support_10d FLOAT;  -- 10日量价支撑

-- 添加价格区间成交量分布指标
ALTER TABLE stock_history ADD COLUMN volume_distribution_high FLOAT;   -- 高位成交量占比（最高1/3价格区间）
ALTER TABLE stock_history ADD COLUMN volume_distribution_mid FLOAT;    -- 中位成交量占比（中间1/3价格区间）
ALTER TABLE stock_history ADD COLUMN volume_distribution_low FLOAT;    -- 低位成交量占比（最低1/3价格区间）

-- 添加突破回踩指标
ALTER TABLE stock_history ADD COLUMN breakthrough_ma5 FLOAT;  -- MA5突破强度（正值表示向上突破，负值表示向下突破）
ALTER TABLE stock_history ADD COLUMN breakthrough_ma10 FLOAT; -- MA10突破强度
ALTER TABLE stock_history ADD COLUMN pullback_ma5 FLOAT;     -- MA5回踩强度
ALTER TABLE stock_history ADD COLUMN pullback_ma10 FLOAT;    -- MA10回踩强度

-- 创建索引以提升查询性能
CREATE INDEX IF NOT EXISTS idx_stock_history_ma5 ON stock_history(ma5);
CREATE INDEX IF NOT EXISTS idx_stock_history_ma10 ON stock_history(ma10);
CREATE INDEX IF NOT EXISTS idx_stock_history_amplitude_5d ON stock_history(amplitude_5d);
CREATE INDEX IF NOT EXISTS idx_stock_history_volume_ratio_5d ON stock_history(volume_ratio_5d);
CREATE INDEX IF NOT EXISTS idx_stock_history_pressure_ma5 ON stock_history(pressure_ma5);
CREATE INDEX IF NOT EXISTS idx_stock_history_support_ma5 ON stock_history(support_ma5); 