"""
更新股票技术指标数据
"""

import os
import sqlite3
import pandas as pd
import numpy as np
from typing import List, Dict
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue
import threading
import argparse
from datetime import datetime

# 添加线程安全的队列用于收集计算结果
result_queue = Queue()

def get_db_path() -> str:
    """获取数据库路径"""
    return os.path.join(os.path.dirname(os.path.dirname(__file__)), 'stock_data.db')

def execute_sql_file(conn: sqlite3.Connection, sql_file: str):
    """执行SQL文件"""
    with open(sql_file, 'r') as f:
        sql_script = f.read()
    
    # 分割SQL语句
    sql_commands = sql_script.split(';')
    
    # 执行每个SQL命令
    for command in sql_commands:
        if command.strip():
            try:
                conn.execute(command)
            except sqlite3.OperationalError as e:
                # 如果是列已存在的错误，我们可以忽略
                if "duplicate column name" not in str(e):
                    print(f"执行SQL命令时出错: {e}")
                    print(f"SQL命令: {command}")
                    raise

def calculate_support_pressure_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """计算支撑压力相关指标
    
    Args:
        df: 包含OHLCV数据的DataFrame
        
    Returns:
        添加了支撑压力指标的DataFrame
    """
    # 计算均线压力和支撑
    for period in [5, 10, 20]:
        ma_col = f'ma{period}'
        pressure_col = f'pressure_{ma_col}'
        support_col = f'support_{ma_col}'
        
        # 计算压力（当价格低于均线时）
        df[pressure_col] = np.where(
            df['close'] < df[ma_col],
            (df[ma_col] - df['close']) / df['close'] * 100 * (df['volume'] / df[f'volume_ma{period}']),
            0
        )
        
        # 计算支撑（当价格高于均线时）
        df[support_col] = np.where(
            df['close'] > df[ma_col],
            (df['close'] - df[ma_col]) / df[ma_col] * 100,
            0
        )
    
    # 计算量价支撑压力
    for period in [5, 10]:
        # 计算时间权重
        time_weights = np.linspace(0.5, 1, period)
        
        # 使用rolling window计算
        for i in range(len(df)):
            if i < period - 1:
                continue
                
            window = df.iloc[i-period+1:i+1]
            current_price = window['close'].iloc[-1]
            
            # 计算价格距离权重
            price_distances = (window['close'] - current_price) / current_price
            price_weights = np.exp(price_distances)
            
            # 计算加权成交量
            weighted_volumes = window['volume'] * time_weights * price_weights
            total_weighted_volume = weighted_volumes.sum()
            
            if total_weighted_volume > 0:
                # 计算压力（高于当前价格的成交量占比）
                pressure_volume = weighted_volumes[window['close'] > current_price].sum()
                df.loc[df.index[i], f'volume_price_pressure_{period}d'] = (pressure_volume / total_weighted_volume) * 100
                
                # 计算支撑（低于当前价格的成交量占比）
                support_volume = weighted_volumes[window['close'] < current_price].sum()
                df.loc[df.index[i], f'volume_price_support_{period}d'] = (support_volume / total_weighted_volume) * 100
    
    # 计算价格区间成交量分布（使用10日周期）
    period = 10
    for i in range(len(df)):
        if i < period - 1:
            continue
            
        window = df.iloc[i-period+1:i+1]
        
        # 计算价格的均值和标准差
        price_mean = window['close'].mean()
        price_std = window['close'].std()
        
        # 使用标准差划分区间
        high_threshold = price_mean + 0.5 * price_std
        low_threshold = price_mean - 0.5 * price_std
        
        # 计算时间权重
        time_weights = np.linspace(0.5, 1, period)
        weighted_volume = window['volume'] * time_weights
        total_volume = weighted_volume.sum()
        
        if total_volume > 0:
            # 计算各区间成交量占比
            high_volume = weighted_volume[window['close'] >= high_threshold].sum()
            low_volume = weighted_volume[window['close'] <= low_threshold].sum()
            mid_volume = weighted_volume[(window['close'] > low_threshold) & 
                                      (window['close'] < high_threshold)].sum()
            
            df.loc[df.index[i], 'volume_distribution_high'] = (high_volume / total_volume) * 100
            df.loc[df.index[i], 'volume_distribution_mid'] = (mid_volume / total_volume) * 100
            df.loc[df.index[i], 'volume_distribution_low'] = (low_volume / total_volume) * 100
    
    # 计算突破和回踩指标
    for period in [5, 10]:
        ma_col = f'ma{period}'
        volume_ma = f'volume_ma{period}'
        breakthrough_col = f'breakthrough_{ma_col}'
        pullback_col = f'pullback_{ma_col}'
        
        # 计算趋势强度
        trend_strength = (df[ma_col].diff() / df[ma_col].shift()) * 100
        
        # 计算成交量确认
        volume_confirm = df['volume'] / df[volume_ma]
        
        # 突破强度（加入成交量和趋势确认）
        df[breakthrough_col] = ((df['close'] - df[ma_col]) / df[ma_col] * 100 * 
                              volume_confirm * (1 + trend_strength/100))
        
        # 回踩强度（考虑成交量和前期趋势）
        df[pullback_col] = df.apply(
            lambda x: (
                abs(x['low'] - x[ma_col]) / x[ma_col] * 100 * 
                (x['volume'] / x[volume_ma]) * 
                (1 + abs(trend_strength.loc[x.name])/100)
                if x['high'] >= x[ma_col] >= x['low']
                else 0
            ),
            axis=1
        )
    
    return df

def calculate_technical_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """计算技术指标
    
    Args:
        df: 包含OHLCV数据的DataFrame
        
    Returns:
        添加了技术指标的DataFrame
    """
    # 计算移动平均线
    for period in [5, 10, 20, 30, 60]:
        df[f'ma{period}'] = df['close'].rolling(window=period).mean()
    
    # 计算成交量移动平均线
    for period in [5, 10, 20]:
        df[f'volume_ma{period}'] = df['volume'].rolling(window=period).mean()
    
    # 计算振幅
    def calculate_amplitude(data: pd.DataFrame, period: int) -> float:
        """计算N日振幅"""
        return ((data['high'].rolling(window=period).max() - 
                data['low'].rolling(window=period).min()) / 
                data['low'].rolling(window=period).min() * 100)
    
    df['amplitude_5d'] = calculate_amplitude(df, 5)
    df['amplitude_10d'] = calculate_amplitude(df, 10)
    
    # 计算日内波动率
    df['daily_volatility'] = (df['high'] - df['low']) / df['close'] * 100
    
    # 计算成交量比率
    for period in [5, 10]:
        df[f'volume_ratio_{period}d'] = (df['volume'] / 
                                        df['volume'].rolling(window=period).mean())
    
    # 计算趋势指标
    df['ma5_deviation'] = (df['close'] - df['ma5']) / df['ma5'] * 100
    df['ma10_deviation'] = (df['close'] - df['ma10']) / df['ma10'] * 100
    df['ma_trend'] = (df['ma5'] - df['ma10']) / df['ma10'] * 100
    
    # 计算支撑压力相关指标
    df = calculate_support_pressure_indicators(df)
    
    return df

def update_stock_indicators_batch(conn: sqlite3.Connection, stock_codes: List[str], current_date: str):
    """批量更新多个股票的技术指标
    
    Args:
        conn: 数据库连接
        stock_codes: 需要更新的股票代码列表
        current_date: 当前日期
    """
    # 计算60天前的日期（用于计算最长的技术指标）
    start_date = (pd.to_datetime(current_date) - pd.Timedelta(days=120)).strftime('%Y-%m-%d')
    
    # 一次性获取所有需要更新的股票的历史数据
    placeholders = ','.join(['?' for _ in stock_codes])
    query = f"""
    SELECT * FROM stock_history 
    WHERE stock_code IN ({placeholders})
    AND date >= ?
    ORDER BY stock_code, date
    """
    
    params = stock_codes + [start_date]
    df = pd.read_sql(query, conn, params=params)
    
    if df.empty:
        return
    
    # 清空结果队列
    while not result_queue.empty():
        result_queue.get()
    
    def process_stock_data(stock_code: str, group: pd.DataFrame) -> Dict:
        """处理单个股票的数据，计算技术指标"""
        try:
            group = group.sort_values('date')
            
            # 计算移动平均线
            for period in [5, 10, 20, 30, 60]:
                group[f'ma{period}'] = group['close'].rolling(window=period).mean()
            
            # 计算成交量移动平均线
            for period in [5, 10, 20]:
                group[f'volume_ma{period}'] = group['volume'].rolling(window=period).mean()
            
            # 计算振幅
            for period in [5, 10]:
                group[f'amplitude_{period}d'] = ((group['high'].rolling(window=period).max() - 
                                                group['low'].rolling(window=period).min()) / 
                                               group['low'].rolling(window=period).min() * 100)
            
            # 计算日内波动率
            group['daily_volatility'] = (group['high'] - group['low']) / group['close'] * 100
            
            # 计算成交量比率
            for period in [5, 10]:
                group[f'volume_ratio_{period}d'] = (group['volume'] / 
                                                  group['volume'].rolling(window=period).mean())
            
            # 计算趋势指标
            group['ma5_deviation'] = (group['close'] - group['ma5']) / group['ma5'] * 100
            group['ma10_deviation'] = (group['close'] - group['ma10']) / group['ma10'] * 100
            group['ma_trend'] = (group['ma5'] - group['ma10']) / group['ma10'] * 100
            
            # 计算支撑压力相关指标
            for period in [5, 10, 20]:
                ma_col = f'ma{period}'
                pressure_col = f'pressure_{ma_col}'
                support_col = f'support_{ma_col}'
                
                # 计算压力（当价格低于均线时）
                group[pressure_col] = np.where(
                    group['close'] < group[ma_col],
                    (group[ma_col] - group['close']) / group['close'] * 100 * 
                    (group['volume'] / group[f'volume_ma{period}']),
                    0
                )
                
                # 计算支撑（当价格高于均线时）
                group[support_col] = np.where(
                    group['close'] > group[ma_col],
                    (group['close'] - group[ma_col]) / group[ma_col] * 100,
                    0
                )
            
            # 简化的量价支撑压力计算（提高性能）
            for period in [5, 10]:
                # 使用简化的计算方法
                rolling_high = group['high'].rolling(window=period).max()
                rolling_low = group['low'].rolling(window=period).min()
                rolling_volume = group['volume'].rolling(window=period).mean()

                # 简化的压力指标：当前价格相对于期间高点的位置
                group[f'volume_price_pressure_{period}d'] = np.where(
                    rolling_high > group['close'],
                    ((rolling_high - group['close']) / group['close'] * 100) *
                    (group['volume'] / rolling_volume),
                    0
                )

                # 简化的支撑指标：当前价格相对于期间低点的位置
                group[f'volume_price_support_{period}d'] = np.where(
                    rolling_low < group['close'],
                    ((group['close'] - rolling_low) / rolling_low * 100) *
                    (group['volume'] / rolling_volume),
                    0
                )
            
            # 简化的价格区间成交量分布计算
            period = 10
            rolling_mean = group['close'].rolling(window=period).mean()
            rolling_std = group['close'].rolling(window=period).std()
            rolling_volume = group['volume'].rolling(window=period).mean()

            # 简化的分布计算
            high_threshold = rolling_mean + 0.5 * rolling_std
            low_threshold = rolling_mean - 0.5 * rolling_std

            # 基于当前价格位置的简化分布
            group['volume_distribution_high'] = np.where(
                group['close'] >= high_threshold,
                (group['volume'] / rolling_volume) * 100,
                0
            )

            group['volume_distribution_low'] = np.where(
                group['close'] <= low_threshold,
                (group['volume'] / rolling_volume) * 100,
                0
            )

            group['volume_distribution_mid'] = np.where(
                (group['close'] > low_threshold) & (group['close'] < high_threshold),
                (group['volume'] / rolling_volume) * 100,
                0
            )
            
            # 简化的突破和回踩指标计算
            for period in [5, 10]:
                ma_col = f'ma{period}'
                volume_ma = f'volume_ma{period}'

                # 简化的趋势强度计算
                trend_strength = (group[ma_col].pct_change() * 100).fillna(0)
                volume_confirm = (group['volume'] / group[volume_ma]).fillna(1)

                # 简化的突破强度
                group[f'breakthrough_{ma_col}'] = ((group['close'] - group[ma_col]) / group[ma_col] * 100 *
                                                 volume_confirm * (1 + trend_strength/100))

                # 简化的回踩强度
                pullback_condition = (group['high'] >= group[ma_col]) & (group[ma_col] >= group['low'])
                group[f'pullback_{ma_col}'] = np.where(
                    pullback_condition,
                    (abs(group['low'] - group[ma_col]) / group[ma_col] * 100 *
                     volume_confirm * (1 + abs(trend_strength)/100)),
                    0
                )
            
            # 获取最新一天的数据
            latest = group[group['date'] == current_date].iloc[0]
            
            # 准备更新数据
            update_data = {
                'ma5': latest['ma5'],
                'ma10': latest['ma10'],
                'ma20': latest['ma20'],
                'ma30': latest['ma30'],
                'ma60': latest['ma60'],
                'volume_ma5': latest['volume_ma5'],
                'volume_ma10': latest['volume_ma10'],
                'volume_ma20': latest['volume_ma20'],
                'amplitude_5d': latest['amplitude_5d'],
                'amplitude_10d': latest['amplitude_10d'],
                'daily_volatility': latest['daily_volatility'],
                'volume_ratio_5d': latest['volume_ratio_5d'],
                'volume_ratio_10d': latest['volume_ratio_10d'],
                'ma5_deviation': latest['ma5_deviation'],
                'ma10_deviation': latest['ma10_deviation'],
                'ma_trend': latest['ma_trend'],
                'pressure_ma5': latest['pressure_ma5'],
                'pressure_ma10': latest['pressure_ma10'],
                'pressure_ma20': latest['pressure_ma20'],
                'support_ma5': latest['support_ma5'],
                'support_ma10': latest['support_ma10'],
                'support_ma20': latest['support_ma20'],
                'volume_price_pressure_5d': latest['volume_price_pressure_5d'],
                'volume_price_pressure_10d': latest['volume_price_pressure_10d'],
                'volume_price_support_5d': latest['volume_price_support_5d'],
                'volume_price_support_10d': latest['volume_price_support_10d'],
                'volume_distribution_high': latest['volume_distribution_high'],
                'volume_distribution_mid': latest['volume_distribution_mid'],
                'volume_distribution_low': latest['volume_distribution_low'],
                'breakthrough_ma5': latest['breakthrough_ma5'],
                'breakthrough_ma10': latest['breakthrough_ma10'],
                'pullback_ma5': latest['pullback_ma5'],
                'pullback_ma10': latest['pullback_ma10']
            }
            
            # 过滤掉None值
            update_data = {k: v for k, v in update_data.items() if pd.notna(v)}
            
            return {
                'stock_code': stock_code,
                'date': current_date,
                'data': update_data
            }
            
        except Exception as e:
            print(f"处理股票 {stock_code} 时出错: {str(e)}")
            return None
    
    # 使用线程池处理数据
    with ThreadPoolExecutor(max_workers=min(8, len(stock_codes))) as executor:
        # 提交所有任务
        future_to_stock = {
            executor.submit(process_stock_data, stock_code, group): stock_code
            for stock_code, group in df.groupby('stock_code')
        }
        
        # 收集结果
        updates = []
        for future in as_completed(future_to_stock):
            stock_code = future_to_stock[future]
            try:
                result = future.result()
                if result:
                    updates.append(result)
            except Exception as e:
                print(f"处理股票 {stock_code} 的结果时出错: {str(e)}")
    
    # 批量更新数据库
    if updates:
        cursor = conn.cursor()
        for update in updates:
            set_clause = ', '.join([f"{k} = ?" for k in update['data'].keys()])
            values = list(update['data'].values())
            values.extend([update['stock_code'], update['date']])
            
            sql = f"""
                UPDATE stock_history 
                SET {set_clause}
                WHERE stock_code = ? AND date = ?
            """
            
            try:
                cursor.execute(sql, values)
            except Exception as e:
                print(f"更新股票 {update['stock_code']} 技术指标时出错: {str(e)}")
                continue
        
        conn.commit()

def update_stock_indicators(conn: sqlite3.Connection, stock_code: str):
    """
    为了保持兼容性，保留单个股票更新的接口
    """
    current_date = pd.read_sql(
        "SELECT MAX(date) as latest_date FROM stock_history WHERE stock_code = ?",
        conn,
        params=(stock_code,)
    )['latest_date'].iloc[0]
    
    update_stock_indicators_batch(conn, [stock_code], current_date)

def calculate_volume_pressure(df: pd.DataFrame, period: int) -> float:
    """改进的量价压力计算
    
    Args:
        df: 历史数据
        period: 计算周期
        
    Returns:
        加权的量价压力指标
    """
    # 计算时间权重（近期数据权重更大）
    time_weights = np.linspace(0.5, 1, period)
    
    # 计算价格距离权重
    price_distances = (df['close'] - df['close'].iloc[-1]) / df['close'].iloc[-1]
    price_weights = np.exp(price_distances)  # 使用指数函数放大距离影响
    
    # 计算加权成交量
    weighted_volumes = df['volume'] * time_weights * price_weights
    
    # 计算压力指标
    pressure_volume = weighted_volumes[df['close'] > df['close'].iloc[-1]].sum()
    total_volume = weighted_volumes.sum()
    
    return (pressure_volume / total_volume) * 100

def calculate_breakthrough_indicators(df: pd.DataFrame, period: int) -> dict:
    """改进的突破回踩指标计算
    
    加入成交量确认和趋势上下文
    """
    ma = f'ma{period}'
    volume_ma = f'volume_ma{period}'
    
    # 计算趋势强度
    trend_strength = (df[ma].diff() / df[ma].shift()) * 100
    
    # 计算成交量确认
    volume_confirm = df['volume'] / df[volume_ma]
    
    # 突破强度（加入成交量和趋势确认）
    breakthrough = ((df['close'] - df[ma]) / df[ma] * 100 * 
                   volume_confirm * (1 + trend_strength/100))
    
    # 回踩强度（考虑成交量和前期趋势）
    pullback = df.apply(
        lambda x: (
            abs(x['low'] - x[ma]) / x[ma] * 100 * 
            (x['volume'] / x[volume_ma]) * 
            (1 + abs(trend_strength)/100)
            if x['high'] >= x[ma] >= x['low']
            else 0
        ),
        axis=1
    )
    
    return {
        'breakthrough': breakthrough,
        'pullback': pullback
    }

def main():
    """主函数"""
    try:
        # 解析命令行参数
        parser = argparse.ArgumentParser(description='更新股票技术指标')
        parser.add_argument('--start_date', type=str, required=True, help='开始日期 (YYYYMMDD)')
        parser.add_argument('--end_date', type=str, required=True, help='结束日期 (YYYYMMDD)')
        args = parser.parse_args()

        # 转换日期格式
        start_date = datetime.strptime(args.start_date, '%Y%m%d').strftime('%Y-%m-%d')
        end_date = datetime.strptime(args.end_date, '%Y%m%d').strftime('%Y-%m-%d')

        # 连接数据库
        db_path = get_db_path()
        conn = sqlite3.connect(db_path)
        print(f"连接到数据库: {db_path}")

        # 执行SQL文件添加新列
        sql_file = os.path.join(os.path.dirname(__file__), 'add_technical_indicators.sql')
        print("添加技术指标字段...")
        execute_sql_file(conn, sql_file)
        conn.commit()

        # 获取指定日期范围内有数据的股票代码（优化：只处理有当日数据的股票）
        cursor = conn.execute("""
            SELECT DISTINCT stock_code
            FROM stock_history
            WHERE date BETWEEN ? AND ?
            AND (ma5 IS NULL OR ma10 IS NULL)
            ORDER BY stock_code
        """, (start_date, end_date))
        stock_codes = [row[0] for row in cursor.fetchall()]

        if not stock_codes:
            print(f"\n在日期范围 {start_date} 到 {end_date} 内没有需要更新的股票数据！")
            print("所有股票的技术指标都已是最新状态。")
            return

        print(f"\n找到 {len(stock_codes)} 只需要更新技术指标的股票")

        # 对每个日期进行处理
        current_date = start_date
        while current_date <= end_date:
            print(f"\n📅 处理日期: {current_date}")

            # 获取当日有数据的股票
            cursor = conn.execute("""
                SELECT DISTINCT stock_code
                FROM stock_history
                WHERE date = ?
                ORDER BY stock_code
            """, (current_date,))
            daily_stock_codes = [row[0] for row in cursor.fetchall()]

            if not daily_stock_codes:
                print(f"   ⚠️  {current_date} 没有股票数据，跳过")
                current_date = (pd.to_datetime(current_date) + pd.Timedelta(days=1)).strftime('%Y-%m-%d')
                continue

            print(f"   📊 处理 {len(daily_stock_codes)} 只股票...")

            # 使用进度条显示处理进度
            with tqdm(total=len(daily_stock_codes), desc="   计算技术指标",
                     bar_format='{desc}: {percentage:3.0f}%|{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]') as pbar:

                # 分批处理，每批50只股票
                batch_size = 50
                for i in range(0, len(daily_stock_codes), batch_size):
                    batch_codes = daily_stock_codes[i:i+batch_size]
                    update_stock_indicators_batch(conn, batch_codes, current_date)
                    conn.commit()
                    pbar.update(len(batch_codes))

            print(f"   ✅ {current_date} 处理完成")

            # 更新日期
            current_date = (pd.to_datetime(current_date) + pd.Timedelta(days=1)).strftime('%Y-%m-%d')

        print("\n🎉 技术指标更新完成！")

    except Exception as e:
        print(f"❌ 发生错误: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    main() 