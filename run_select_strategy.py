"""
股票选股策略运行脚本

使用流程：
1、下载当日盘后数据
python3 scripts/download_efinance_history.py --market sh --start_date 20250530 --end_date 20250603
2、计算当日技术指标
python3 scripts/update_technical_indicators.py --start_date 20250530 --end_date 20250603
3、执行选股并将选股结果写入数据库（可选）
python3 strategies/stock_picker/batch_stock_selection.py
4、执行单日选股，并输出选股结果
python3 run_select_strategy.py
5、交易策略回测
python3 run_stock_strategy.py

最新改动：移除了选股结果的缓存数据库的使用，后续回测仍然需要加回来
"""


import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from strategies.stock_picker.strategies.select_strategy_1 import SelectStrategy1

def main():
    try:
        # 创建策略实例
        strategy = SelectStrategy1()
        
        # 获取最新交易日期
        latest_date = strategy.db.get_latest_trading_date()
        print(f"\n正在运行选股策略：选股策略一")
        print(f"选股日期：{latest_date}")
        print("\n开始选股...")
        
        # 运行选股策略 - 包含沪深两市
        selected_sh = strategy.run(date=latest_date, market='sh')
        selected_sz = strategy.run(date=latest_date, market='sz')
        
        # 合并结果
        import pandas as pd
        selected = pd.concat([selected_sh, selected_sz])
        
        if not selected.empty:
            # 保留需要显示的列
            display_columns = [
                'stock_code', 'stock_name', 'close', 'amount', 'amplitude_5d',
                'trend_score', 'volume_score', 'pressure_support_score', 'momentum_score',
                'total_score', 'suggested_buy_price'
            ]
            selected = selected[display_columns]
            
            # 按总分排序
            selected = selected.sort_values('total_score', ascending=False)
            # 重置索引从1开始
            selected.index = range(1, len(selected) + 1)
            
            print(f"\n选股结果：共选出 {len(selected)} 只股票")
            print("\n序号  股票代码    名称    现价    成交额    振幅   趋势  量能  压支  动量   总分  建议买入")
            print("-" * 95)
            
            # 格式化输出每一行
            for idx, row in selected.iterrows():
                # 统一股票代码格式
                stock_code = row['stock_code']
                if not stock_code.startswith(('sh.', 'sz.')):
                    if stock_code.startswith('6'):
                        stock_code = 'sh.' + stock_code
                    else:
                        stock_code = 'sz.' + stock_code
                        
                amount_str = f"{row['amount']/1e8:.2f}亿"
                print(
                    f"{idx:2d}  {stock_code:<9s} {row['stock_name']:<8s} "
                    f"{row['close']:6.2f}  {amount_str:>7s}  "
                    f"{row['amplitude_5d']:5.1f}%  "
                    f"{row['trend_score']:4.1f} {row['volume_score']:4.1f} "
                    f"{row['pressure_support_score']:4.1f} {row['momentum_score']:4.1f}  "
                    f"{row['total_score']:5.1f}  {row['suggested_buy_price']:6.2f}"
                )
            
            # 打印评分说明
            print("\n评分说明：")
            print("趋势 - 趋势得分（30分）：均线趋势和价格位置")
            print("量能 - 成交量得分（30分）：成交额排名和成交量稳定性")
            print("压支 - 压力支撑得分（30分）：量价压力和支撑强度")
            print("动量 - 动量指标得分（20分）：突破趋势和回踩支撑")
            print("\n建议买入价说明：")
            print("1. 考虑了量价压力、动量和趋势等多个因素")
            print("2. 建议在实际买入时设置价格区间：建议买入价 ±2%")
            print("3. 建议分批买入，可以在建议价格以下逐步建仓")
            
        else:
            print("\n未找到符合条件的股票")
            
    except Exception as e:
        print(f"运行过程中发生错误: {str(e)}")

if __name__ == '__main__':
    main() 