import pandas as pd
import numpy as np
from typing import List, Dict, Optional
from datetime import datetime, timedelta
from ..utils.db_utils import DBUtils
from ..stock_picker.base import BaseStockPicker
from ..trader.base import BaseTrader

class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, 
                 stock_picker: BaseStockPicker,
                 trader: BaseTrader,
                 start_date: str,
                 end_date: str,
                 initial_capital: float = 1000000.0,
                 commission_rate: float = 0.0003,
                 slippage_rate: float = 0.0001):
        """
        Args:
            stock_picker: 选股策略
            trader: 交易策略
            start_date: 回测开始日期，格式：YYYY-MM-DD
            end_date: 回测结束日期，格式：YYYY-MM-DD
            initial_capital: 初始资金
            commission_rate: 手续费率
            slippage_rate: 滑点率
        """
        self.stock_picker = stock_picker
        self.trader = trader
        self.start_date = start_date
        self.end_date = end_date
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage_rate = slippage_rate
        
        self.db = DBUtils()
        self.current_capital = initial_capital
        self.positions: Dict[str, Dict] = {}  # {股票代码: {数量, 成本价}}
        self.trade_history: List[Dict] = []
        self.daily_net_value: List[Dict] = []
        
    def get_trading_dates(self) -> List[str]:
        """获取交易日期列表"""
        # 这里简化处理，直接获取数据库中的所有交易日
        query = f"""
        SELECT DISTINCT date 
        FROM daily_data 
        WHERE date BETWEEN '{self.start_date}' AND '{self.end_date}'
        ORDER BY date
        """
        dates = pd.read_sql(query, self.db.get_connection())
        return dates['date'].tolist()
        
    def calculate_transaction_cost(self, amount: float) -> float:
        """计算交易成本
        
        Args:
            amount: 交易金额
            
        Returns:
            float: 交易成本
        """
        commission = amount * self.commission_rate
        slippage = amount * self.slippage_rate
        return commission + slippage
        
    def execute_trades(self, signals: pd.DataFrame, date: str):
        """执行交易
        
        Args:
            signals: 交易信号DataFrame
            date: 交易日期
        """
        current_prices = self.db.get_stock_data_by_date(date).set_index('code')
        
        # 处理卖出信号
        for code in self.positions.copy():
            if code in signals.index and signals.loc[code, 'signal'] == -1:
                position = self.positions[code]
                sell_price = current_prices.loc[code, 'close'] * (1 - self.slippage_rate)
                sell_amount = position['shares'] * sell_price
                cost = self.calculate_transaction_cost(sell_amount)
                
                self.current_capital += (sell_amount - cost)
                del self.positions[code]
                
                self.trade_history.append({
                    'date': date,
                    'code': code,
                    'action': 'sell',
                    'price': sell_price,
                    'shares': position['shares'],
                    'amount': sell_amount,
                    'cost': cost
                })
        
        # 处理买入信号
        buy_signals = signals[signals['signal'] == 1]
        total_weight = buy_signals['weight'].sum()
        
        for code, row in buy_signals.iterrows():
            if code not in current_prices.index:
                continue
                
            weight = row['weight'] / total_weight
            amount = self.current_capital * weight
            buy_price = current_prices.loc[code, 'close'] * (1 + self.slippage_rate)
            shares = int(amount / buy_price)
            actual_amount = shares * buy_price
            cost = self.calculate_transaction_cost(actual_amount)
            
            if actual_amount + cost <= self.current_capital:
                self.current_capital -= (actual_amount + cost)
                self.positions[code] = {
                    'shares': shares,
                    'cost': buy_price
                }
                
                self.trade_history.append({
                    'date': date,
                    'code': code,
                    'action': 'buy',
                    'price': buy_price,
                    'shares': shares,
                    'amount': actual_amount,
                    'cost': cost
                })
                
    def calculate_daily_net_value(self, date: str):
        """计算每日净值
        
        Args:
            date: 交易日期
        """
        current_prices = self.db.get_stock_data_by_date(date).set_index('code')
        position_value = 0
        
        for code, position in self.positions.items():
            if code in current_prices.index:
                current_price = current_prices.loc[code, 'close']
                position_value += position['shares'] * current_price
                
        total_value = self.current_capital + position_value
        
        self.daily_net_value.append({
            'date': date,
            'net_value': total_value,
            'return_rate': (total_value / self.initial_capital - 1) * 100
        })
        
    def run(self) -> pd.DataFrame:
        """运行回测
        
        Returns:
            DataFrame: 回测结果统计
        """
        trading_dates = self.get_trading_dates()
        print(f"开始回测：{self.start_date} 至 {self.end_date}")
        print(f"初始资金：{self.initial_capital:,.2f}")
        
        for date in trading_dates:
            # 选股
            stock_pool = self.stock_picker.pick_stocks(date)
            
            # 生成交易信号
            if not stock_pool.empty:
                signals = self.trader.run(stock_pool, date)
                
                # 执行交易
                self.execute_trades(signals, date)
            
            # 计算每日净值
            self.calculate_daily_net_value(date)
            
        # 生成回测报告
        return self.generate_report()
        
    def generate_report(self) -> pd.DataFrame:
        """生成回测报告
        
        Returns:
            DataFrame: 回测统计数据
        """
        daily_returns = pd.DataFrame(self.daily_net_value)
        
        # 计算各项指标
        total_return = daily_returns['return_rate'].iloc[-1]
        annual_return = total_return * 365 / len(daily_returns)
        
        daily_return_series = daily_returns['net_value'].pct_change()
        volatility = daily_return_series.std() * np.sqrt(252) * 100
        sharpe_ratio = (annual_return - 2.5) / volatility  # 假设无风险利率为2.5%
        
        max_drawdown = 0
        peak = daily_returns['net_value'].iloc[0]
        for value in daily_returns['net_value']:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak * 100
            max_drawdown = max(max_drawdown, drawdown)
        
        stats = {
            '开始日期': self.start_date,
            '结束日期': self.end_date,
            '初始资金': self.initial_capital,
            '结束资金': daily_returns['net_value'].iloc[-1],
            '总收益率(%)': total_return,
            '年化收益率(%)': annual_return,
            '最大回撤(%)': max_drawdown,
            '夏普比率': sharpe_ratio,
            '波动率(%)': volatility,
            '交易次数': len(self.trade_history)
        }
        
        return pd.DataFrame([stats]).T.rename(columns={0: '统计值'})

# 使用示例
if __name__ == '__main__':
    from datetime import datetime, timedelta
    
    # 创建回测引擎实例（需要先实现具体的选股和交易策略类）
    engine = BacktestEngine(
        stock_picker=None,  # 需要实现具体的选股策略
        trader=None,        # 需要实现具体的交易策略
        start_date='2023-01-01',
        end_date='2023-12-31',
        initial_capital=1000000.0
    )
    
    # 运行回测
    results = engine.run()
    print("\n回测结果：")
    print(results)
