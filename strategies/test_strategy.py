"""
测试选股策略的脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategies.stock_picker.strategies.select_strategy_1 import SelectStrategy1
from strategies.utils.db_utils import DBUtils
import pandas as pd

def test_strategy_1():
    # 创建策略实例
    strategy = SelectStrategy1()
    db = DBUtils()
    
    # 获取最新交易日期
    latest_date = db.get_latest_trading_date()
    print(f"测试日期：{latest_date}")
    
    # 分别测试上证和深证
    for market in ['sh', 'sz']:
        print(f"\n测试{market.upper()}市场：")
        
        # 获取该市场的所有股票
        stocks = db.get_stock_list(market)
        print(f"该市场共有 {len(stocks)} 只股票")
        
        # 获取当日数据
        daily_data = db.get_stock_data_by_date(latest_date, market)
        print(f"当日有交易数据的股票数量：{len(daily_data)}")
        
        # 打印一些数据统计
        if not daily_data.empty:
            print("\n数据统计：")
            print(f"成交额范围：{daily_data['amount'].min():.2f} - {daily_data['amount'].max():.2f}")
            print(f"成交量范围：{daily_data['volume'].min():.2f} - {daily_data['volume'].max():.2f}")
        
        # 运行选股策略
        selected = strategy.run(date=latest_date, market=market)
        
        if not selected.empty:
            print(f"\n共选出 {len(selected)} 只股票：")
            # 格式化输出，使数字更易读
            pd.set_option('display.float_format', lambda x: '%.2f' if x % 1 else '%.0f')
            
            # 格式化成交额（以亿为单位）
            selected['amount'] = selected['amount'] / 100000000
            
            # 创建格式化函数
            def format_row(row):
                return pd.Series({
                    'stock_code': row['stock_code'],
                    'name': row['name'],
                    'close': f"{row['close']:.2f}",
                    'amount': f"{row['amount']:.2f}亿",
                    'amplitude': f"{row['amplitude']:.2f}%"
                })
            
            # 应用格式化
            formatted = selected.apply(format_row, axis=1)
            print(formatted.to_string())
        else:
            print("没有找到符合条件的股票")

if __name__ == '__main__':
    pd.set_option('display.max_rows', None)
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)
    
    print("开始测试选股策略1...")
    test_strategy_1() 