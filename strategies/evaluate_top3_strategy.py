"""
评估选股策略前三名组合的收益率
"""

import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List
import pandas as pd

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from stock_picker.strategies.select_strategy_1 import SelectStrategy1
from utils.db_utils import DBUtils

class Top3Evaluator:
    def __init__(self):
        self.stock_picker = SelectStrategy1()
        self.db = DBUtils()
        
    def get_trading_days_between(self, start_date: str, end_date: str) -> List[str]:
        """获取指定日期范围内的所有交易日"""
        query = f"""
        SELECT DISTINCT date 
        FROM stock_history 
        WHERE date >= '{start_date}' AND date <= '{end_date}'
        ORDER BY date
        """
        conn = self.db.get_connection()
        return pd.read_sql(query, conn)['date'].tolist()
        
    def get_next_n_trading_days(self, start_date: str, n: int) -> List[str]:
        """获取从start_date开始的n个交易日"""
        query = f"""
        SELECT DISTINCT date 
        FROM stock_history 
        WHERE date > '{start_date}'
        ORDER BY date
        LIMIT {n}
        """
        conn = self.db.get_connection()
        return pd.read_sql(query, conn)['date'].tolist()
        
    def calculate_returns(self, stock_code: str, buy_date: str, future_dates: List[str]) -> Dict[int, float]:
        """计算单只股票在不同时间段的收益率"""
        # 获取买入价格
        buy_data = self.db.get_daily_data(stock_code, end_date=buy_date)
        if buy_data.empty:
            return {}
        buy_price = buy_data.iloc[-1]['close']
        
        # 获取未来价格
        returns = {}
        for i, future_date in enumerate(future_dates, 1):
            future_data = self.db.get_daily_data(stock_code, end_date=future_date)
            if not future_data.empty:
                future_price = future_data.iloc[-1]['close']
                returns[i] = (future_price - buy_price) / buy_price * 100
                
        return returns

    def evaluate_strategy(self, date: str):
        """评估策略在指定日期的表现"""
        # 获取选股结果
        selected_sh = self.stock_picker.run(date=date, market='sh', print_info=False)
        selected_sz = self.stock_picker.run(date=date, market='sz', print_info=False)
        selected = pd.concat([selected_sh, selected_sz])
        
        if selected.empty:
            return None
            
        # 获取未来5个交易日
        future_dates = self.get_next_n_trading_days(date, 5)
        if len(future_dates) < 5:
            return None
            
        # 选取得分最高的前3只股票
        top_stocks = selected.nlargest(3, 'total_score')
        
        # 计算每只股票的收益率
        results = []
        portfolio_value = 1000000  # 100万初始资金
        per_stock_value = portfolio_value / 3  # 每只股票分配相同资金
        
        for _, stock in top_stocks.iterrows():
            stock_code = stock['stock_code']
            returns = self.calculate_returns(stock_code, date, future_dates)
            
            # 计算持仓数量
            buy_price = stock['close']
            shares = int(per_stock_value / buy_price)  # 向下取整，模拟实际可买入数量
            actual_value = shares * buy_price
            
            result = {
                'stock_code': stock_code,
                'stock_name': stock['stock_name'],
                'score': stock['total_score'],
                'buy_price': buy_price,
                'shares': shares,
                'position_value': actual_value
            }
            
            # 添加1日、3日、5日收益率
            for days in [1, 3, 5]:
                if days <= len(future_dates):
                    returns_data = self.calculate_returns(stock_code, date, [future_dates[days-1]])
                    if 1 in returns_data:
                        result[f'{days}日收益率'] = returns_data[1]
                        result[f'{days}日市值'] = actual_value * (1 + returns_data[1]/100)
                    else:
                        result[f'{days}日收益率'] = None
                        result[f'{days}日市值'] = None
                else:
                    result[f'{days}日收益率'] = None
                    result[f'{days}日市值'] = None
                    
            results.append(result)
            
        # 转换为DataFrame
        results_df = pd.DataFrame(results)
        
        # 计算组合总市值和收益率
        initial_portfolio = results_df['position_value'].sum()
        returns_data = {}
        
        # 计算组合收益
        for days in [1, 3, 5]:
            if f'{days}日市值' in results_df.columns and not results_df[f'{days}日市值'].isnull().all():
                portfolio_value = results_df[f'{days}日市值'].sum()
                portfolio_return = (portfolio_value - initial_portfolio) / initial_portfolio * 100
                returns_data[f'{days}日收益率'] = portfolio_return
                
        return {
            'date': date,
            'stocks': [f"{row['stock_code']}({row['stock_name']})" for _, row in results_df.iterrows()],
            'initial_value': initial_portfolio,
            **returns_data
        }

    def evaluate_date_range(self, start_date: str, end_date: str):
        """评估一段时间内的策略表现"""
        print(f"\n=== 评估区间: {start_date} 至 {end_date} ===\n")
        
        # 获取交易日列表
        trading_days = self.get_trading_days_between(start_date, end_date)
        print(f"共有 {len(trading_days)} 个交易日\n")
        
        # 存储每日评估结果
        all_results = []
        
        # 评估每一天
        for date in trading_days:
            result = self.evaluate_strategy(date)
            if result:
                all_results.append(result)
                
        if not all_results:
            print("没有找到有效的评估结果")
            return
            
        # 转换为DataFrame进行统计分析
        results_df = pd.DataFrame(all_results)
        
        # 计算胜率和平均收益
        print("=== 策略表现统计 ===\n")
        for days in [1, 3, 5]:
            col = f'{days}日收益率'
            if col in results_df.columns:
                win_rate = (results_df[col] > 0).mean() * 100
                avg_return = results_df[col].mean()
                max_return = results_df[col].max()
                min_return = results_df[col].min()
                
                print(f"{days}日统计:")
                print(f"胜率: {win_rate:.2f}%")
                print(f"平均收益: {avg_return:+.2f}%")
                print(f"最大收益: {max_return:+.2f}%")
                print(f"最大回撤: {min_return:+.2f}%")
                print()
                
        # 输出每日详细结果
        print("\n=== 每日选股及收益详情 ===\n")
        for _, row in results_df.iterrows():
            print(f"日期: {row['date']}")
            print(f"选股: {', '.join(row['stocks'])}")
            for days in [1, 3, 5]:
                col = f'{days}日收益率'
                if col in row:
                    print(f"{days}日收益率: {row[col]:+.2f}%")
            print()

def main():
    evaluator = Top3Evaluator()
    
    # 评估指定日期范围
    start_date = "2025-01-16"
    end_date = "2025-05-20"
    
    # 评估策略
    evaluator.evaluate_date_range(start_date, end_date)

if __name__ == "__main__":
    main() 