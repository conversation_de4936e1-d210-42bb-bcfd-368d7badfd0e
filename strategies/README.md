# 策略模块说明

## 目录结构
```
strategies/
├── README.md                 # 本说明文档
├── utils/                    # 工具函数目录
│   ├── __init__.py
│   └── db_utils.py          # 数据库访问工具
├── stock_picker/            # 选股策略目录
│   ├── __init__.py
│   ├── base.py             # 选股策略基类
│   └── strategies/         # 具体选股策略实现
│       └── __init__.py
├── trader/                  # 交易策略目录
│   ├── __init__.py
│   ├── base.py            # 交易策略基类
│   └── strategies/        # 具体交易策略实现
│       └── __init__.py
└── backtest/               # 回测模块目录
    ├── __init__.py
    └── engine.py          # 回测引擎

## 模块说明

### 1. 选股策略 (stock_picker)
- 负责从全市场股票中筛选出符合条件的股票池
- 支持多个选股策略的组合
- 每个策略可以设置独立的选股条件和参数
- 选股结果可以输出到数据库或文件中

### 2. 交易策略 (trader)
- 负责具体的买入卖出信号生成
- 支持设置止损止盈条件
- 支持设置仓位管理规则
- 可以对接不同的交易接口

### 3. 回测模块 (backtest)
- 提供历史数据回测功能
- 支持多策略组合回测
- 生成回测报告和绩效分析

### 4. 工具模块 (utils)
- 提供数据库访问接口
- 提供常用的技术指标计算
- 提供数据预处理功能

## 使用说明

### 数据库配置
数据库连接配置在 `utils/db_utils.py` 中设置，支持动态修改数据库路径。

### 开发新策略
1. 选股策略开发：
   - 在 `stock_picker/strategies/` 下创建新的策略文件
   - 继承 `stock_picker/base.py` 中的基类
   - 实现必要的选股方法

2. 交易策略开发：
   - 在 `trader/strategies/` 下创建新的策略文件
   - 继承 `trader/base.py` 中的基类
   - 实现必要的交易方法

### 回测
1. 在 `backtest/engine.py` 中设置回测参数
2. 选择要回测的选股策略和交易策略
3. 运行回测并查看结果 