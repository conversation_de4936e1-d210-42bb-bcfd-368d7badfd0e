from ..utils.db_utils import DBUtils

class MarketSentimentStrategy:
    def __init__(self):
        self.db_utils = DBUtils()
        
    def get_market_sentiment(self, date=None):
        """获取市场情绪"""
        try:
            # 获取当日所有股票数据
            stocks_data = self.db_utils.get_stock_data(date)
            
            if stocks_data.empty:
                return {
                    'total_stocks': 0,
                    'up_stocks': 0,
                    'down_stocks': 0,
                    'up_ratio': 50.00,
                    'sentiment': 'neutral'
                }
            
            # 计算上涨下跌家数
            total_stocks = len(stocks_data)
            up_stocks = len(stocks_data[stocks_data['change_pct'] > 0])
            down_stocks = len(stocks_data[stocks_data['change_pct'] < 0])
            up_ratio = (up_stocks / total_stocks) * 100 if total_stocks > 0 else 50.00
            
            # 判断市场情绪
            if up_ratio >= 60:
                sentiment = 'bull'
            elif up_ratio <= 40:
                sentiment = 'bear'
            else:
                sentiment = 'neutral'
            
            return {
                'total_stocks': total_stocks,
                'up_stocks': up_stocks,
                'down_stocks': down_stocks,
                'up_ratio': up_ratio,
                'sentiment': sentiment
            }
            
        except Exception as e:
            print(f"获取市场情绪时出错: {str(e)}")
            return {
                'total_stocks': 0,
                'up_stocks': 0,
                'down_stocks': 0,
                'up_ratio': 50.00,
                'sentiment': 'neutral'
            } 