"""
整合选股策略和分仓交易策略的运行脚本 - 策略2
基于评分变化和趋势跟踪的买卖策略，强调风险控制和利润保护
"""

import sys
import os
from datetime import datetime, timedelta
from typing import Dict, Optional, List
import pandas as pd
import numpy as np

from .strategies.select_strategy_1 import SelectStrategy1
from ..utils.db_utils import DBUtils

class IntegratedStrategy2:
    def __init__(self):
        self.stock_picker = SelectStrategy1()
        self.db = DBUtils()
        self.positions = {}  # 当前持仓
        self.position_history = {}  # 记录持仓股票的历史评分和价格
        self.cash = 1000000  # 初始资金100万
        self.total_value = self.cash  # 总资产
        self.max_positions = 8  # 最多持有8只股票
        self.max_position_ratio = 0.12  # 单只股票最大仓位12%
        self.initial_position_ratio = 0.06  # 首次建仓仓位6%
        self.last_trade_date = {}  # 记录每只股票最后交易日期
        self.min_holding_days = 3  # 最小持仓天数
        self.max_loss_ratio = 0.08  # 最大允许回撤8%
        self.profit_target = 0.15  # 目标利润15%
        self.volatility_threshold = 0.45  # 波动率阈值放宽到45%
        self.price_position_threshold = 0.75  # 价格位置阈值放宽到75%

    def check_entry_signal(self, stock_data: pd.Series, history_data: pd.DataFrame) -> bool:
        """检查入场信号"""
        # 1. 趋势确认 - 使用多重均线系统
        ma5 = history_data['close'].rolling(5).mean().iloc[-1]
        ma10 = history_data['close'].rolling(10).mean().iloc[-1]
        ma20 = history_data['close'].rolling(20).mean().iloc[-1]
        ma30 = history_data['close'].rolling(30).mean().iloc[-1]
        
        # 检查均线多头排列和趋势强度
        trend_aligned = (ma5 > ma10 > ma20 > ma30)
        trend_strength = (ma5 - ma30) / ma30 * 100
        
        # 2. 量价配合
        volume_ma5 = history_data['volume'].rolling(5).mean()
        volume_ma10 = history_data['volume'].rolling(10).mean()
        volume_ma20 = history_data['volume'].rolling(20).mean()
        
        # 检查成交量趋势
        latest_volume = history_data['volume'].iloc[-1]
        volume_trend = (volume_ma5.iloc[-1] > volume_ma10.iloc[-1] and 
                       volume_ma10.iloc[-1] > volume_ma20.iloc[-1])
        
        # 3. 价格形态分析
        current_price = stock_data['close']
        recent_high = history_data['high'].rolling(20).max().iloc[-1]
        recent_low = history_data['low'].rolling(20).min().iloc[-1]
        
        # 计算价格位置和波动率
        price_position = (current_price - recent_low) / (recent_high - recent_low) if (recent_high - recent_low) > 0 else 0.5
        volatility = history_data['close'].pct_change().std() * np.sqrt(252)  # 年化波动率
        
        # 4. 动量指标
        roc_5 = (current_price / history_data['close'].iloc[-5] - 1) * 100  # 5日变化率
        roc_10 = (current_price / history_data['close'].iloc[-10] - 1) * 100  # 10日变化率
        
        # 5. 评分条件
        trend_score = stock_data['trend_score']
        volume_score = stock_data['volume_score']
        pressure_support_score = stock_data['pressure_support_score']
        
        # 综合评分条件
        score_condition = (trend_score >= 22 or 
                         (trend_score >= 20 and volume_score >= 22) or
                         (trend_score >= 20 and pressure_support_score >= 22))
        
        # 6. 趋势突破确认
        price_above_ma = current_price > ma20
        volume_breakout = latest_volume > volume_ma20.iloc[-1] * 1.3
        
        # 建议增加短期趋势判断
        short_trend_good = (ma5 > ma10) and (current_price > ma5)
        medium_trend_good = (ma10 > ma20) and (current_price > ma10)
        trend_condition = short_trend_good or medium_trend_good
        
        # 打印调试信息
        if score_condition:
            print(f"\n分析股票 {stock_data['stock_code']}:")
            print(f"趋势评分: {trend_score:.1f}, 成交量评分: {volume_score:.1f}, 压力支撑评分: {pressure_support_score:.1f}")
            print(f"均线多头排列: {trend_aligned}, 趋势强度: {trend_strength:.1f}%")
            print(f"价格位置: {price_position:.2f}, 波动率: {volatility:.2f}")
            print(f"5日变化率: {roc_5:.1f}%, 10日变化率: {roc_10:.1f}%")
            print(f"成交量突破: {volume_breakout}, 量价趋势: {volume_trend}")
        
        # 入场条件组合
        strong_trend = (trend_aligned and trend_strength > 2.0 and price_above_ma)
        momentum_entry = (roc_5 > 0 and roc_10 > 0 and price_position < 0.7)
        volume_confirm = (volume_trend or 
                         (latest_volume > volume_ma10.iloc[-1] * 1.1))
        low_risk = (volatility < 0.4 and price_position < 0.8)
        
        return (score_condition and 
                (strong_trend or momentum_entry) and 
                volume_confirm and 
                low_risk)

    def calculate_position_size(self, stock_code: str, stock_data: pd.Series) -> float:
        """计算仓位大小"""
        if stock_code not in self.positions:
            # 首次建仓，基于波动率和评分动态调整初始仓位
            volatility = stock_data.get('volatility', 0.3)  # 默认波动率30%
            score_weight = (stock_data['trend_score'] + stock_data['volume_score']) / 200
            
            # 波动率调整系数：波动率越高，仓位越小
            vol_adjust = max(0.5, 1 - volatility)
            base_ratio = self.initial_position_ratio * vol_adjust * (0.8 + score_weight)
            
            return min(base_ratio, self.max_position_ratio)
        
        # 已有持仓，考虑加仓
        position = self.positions[stock_code]
        current_ratio = position['ratio']
        trend_score = stock_data['trend_score']
        volume_score = stock_data['volume_score']
        
        # 计算评分改善程度
        if stock_code in self.position_history:
            prev_trend = self.position_history[stock_code]['scores'][-1]
            score_improve = (trend_score - prev_trend) / prev_trend if prev_trend > 0 else 0
        else:
            score_improve = 0
            
        # 根据评分改善和当前趋势强度决定加仓比例
        if (trend_score >= 30 and volume_score >= 25 and 
            score_improve > 0.1 and current_ratio < self.max_position_ratio):
            return min(current_ratio + 0.03, self.max_position_ratio)
            
        return current_ratio

    def should_add_position(self, stock_code: str, stock_data: pd.Series, history_data: pd.DataFrame) -> bool:
        """判断是否应该加仓"""
        if stock_code not in self.positions:
            return False
            
        position = self.positions[stock_code]
        
        # 检查加仓间隔
        last_trade = self.last_trade_date.get(stock_code)
        if last_trade and (datetime.now() - last_trade).days < self.min_holding_days:
            return False
            
        # 获取价格和趋势数据
        current_price = stock_data['close']
        ma20 = history_data['close'].rolling(20).mean().iloc[-1]
        ma50 = history_data['close'].rolling(50).mean().iloc[-1]
        
        # 计算利润率
        profit_ratio = (current_price - position['price']) / position['price']
        
        # 检查趋势强度
        trend_strength = (current_price - ma50) / ma50 * 100
        
        # 获取评分变化
        current_score = stock_data['total_score']
        prev_score = self.position_history[stock_code]['scores'][-1] if stock_code in self.position_history else current_score
        score_change = (current_score - prev_score) / prev_score if prev_score > 0 else 0
        
        # 加仓条件：
        # 1. 盈利且趋势向上
        profit_trend_good = profit_ratio > 0.05 and current_price > ma20 > ma50
        
        # 2. 评分改善
        score_improving = score_change > 0.05
        
        # 3. 趋势强度足够
        trend_strong = trend_strength > 3.0
        
        # 4. 成交量支撑
        volume_ma5 = history_data['volume'].rolling(5).mean().iloc[-1]
        volume_ma20 = history_data['volume'].rolling(20).mean().iloc[-1]
        volume_support = volume_ma5 > volume_ma20
        
        return (profit_trend_good and 
                score_improving and 
                trend_strong and 
                volume_support)

    def calculate_stop_loss(self, stock_code: str, stock_data: pd.Series, history_data: pd.DataFrame) -> Dict:
        """计算止损位置和止损比例"""
        position = self.positions[stock_code]
        entry_price = position['price']
        current_price = stock_data['close']
        profit_ratio = (current_price - entry_price) / entry_price
        
        # 计算ATR
        high = history_data['high']
        low = history_data['low']
        close = history_data['close']
        tr = pd.DataFrame({
            'hl': high - low,
            'hc': abs(high - close.shift(1)),
            'lc': abs(low - close.shift(1))
        }).max(axis=1)
        atr = tr.rolling(14).mean().iloc[-1]
        
        # 获取评分变化
        current_score = stock_data['total_score']
        prev_score = self.position_history[stock_code]['scores'][-1] if stock_code in self.position_history else current_score
        score_change = (current_score - prev_score) / prev_score if prev_score > 0 else 0
        
        # 计算移动止损
        if profit_ratio >= self.profit_target:  # 盈利达到目标
            return {
                'stop_loss_price': max(entry_price * 1.1, current_price - 2 * atr),
                'stop_ratio': 0.7  # 大比例止盈
            }
        elif profit_ratio >= 0.1:  # 盈利超过10%
            if score_change >= 0:  # 评分上升
                return {
                    'stop_loss_price': max(entry_price * 1.05, current_price - 1.5 * atr),
                    'stop_ratio': 0.5
                }
            else:  # 评分下降
                return {
                    'stop_loss_price': max(entry_price * 1.05, current_price - atr),
                    'stop_ratio': 0.7
                }
        elif profit_ratio >= 0.05:  # 盈利超过5%
            if score_change >= 0:
                return {
                    'stop_loss_price': max(entry_price * 1.02, current_price - 1.5 * atr),
                    'stop_ratio': 0.3
                }
            else:
                return {
                    'stop_loss_price': max(entry_price * 1.02, current_price - atr),
                    'stop_ratio': 0.5
                }
        else:  # 小盈利或亏损
            if score_change < -0.1 or profit_ratio < -self.max_loss_ratio:  # 评分显著下降或超过最大亏损
                return {
                    'stop_loss_price': current_price * 0.98,  # 接近现价止损
                    'stop_ratio': 1.0  # 全部止损
                }
            else:
                return {
                    'stop_loss_price': entry_price * (1 - self.max_loss_ratio),  # 最大亏损止损
                    'stop_ratio': 0.5  # 部分止损
                }

    def execute_trades(self, selected_stocks: pd.DataFrame, date: str):
        """执行交易"""
        # 更新持仓股票的评分历史
        for stock_code in self.positions:
            stock_data = selected_stocks[selected_stocks['stock_code'] == stock_code]
            if not stock_data.empty:
                if stock_code not in self.position_history:
                    self.position_history[stock_code] = {'scores': [], 'prices': []}
                self.position_history[stock_code]['scores'].append(stock_data['total_score'].iloc[0])
                self.position_history[stock_code]['prices'].append(stock_data['close'].iloc[0])

        # 处理现有持仓
        for stock_code in list(self.positions.keys()):
            stock_data = selected_stocks[selected_stocks['stock_code'] == stock_code]
            if stock_data.empty:
                # 股票不在选股结果中，考虑清仓
                self._sell_stock(stock_code, self.positions[stock_code]['shares'], '股票跌出选股范围')
                continue

            stock_data = stock_data.iloc[0]
            history_data = self.db.get_stock_history(stock_code, end_date=date, days=50)
            
            # 获取止损参数
            stop_loss = self.calculate_stop_loss(stock_code, stock_data, history_data)
            current_price = stock_data['close']
            
            # 检查止损条件
            if current_price <= stop_loss['stop_loss_price']:
                shares_to_sell = int(self.positions[stock_code]['shares'] * stop_loss['stop_ratio'])
                self._sell_stock(stock_code, shares_to_sell, '触发止损')
                continue
            
            # 检查加仓条件
            if self.should_add_position(stock_code, stock_data, history_data):
                new_ratio = self.calculate_position_size(stock_code, stock_data)
                current_ratio = self.positions[stock_code]['ratio']
                if new_ratio > current_ratio:
                    self._adjust_position(stock_code, stock_data, new_ratio)

        # 处理新建仓位
        available_positions = self.max_positions - len(self.positions)
        if available_positions > 0:
            # 按评分排序并考虑行业分散
            candidate_stocks = selected_stocks[~selected_stocks['stock_code'].isin(self.positions.keys())]
            candidate_stocks = candidate_stocks.sort_values(['total_score', 'trend_score'], ascending=[False, False])

            for _, stock_data in candidate_stocks.head(available_positions).iterrows():
                stock_code = stock_data['stock_code']
                history_data = self.db.get_stock_history(stock_code, end_date=date, days=50)
                
                # 检查入场条件
                if self.check_entry_signal(stock_data, history_data):
                    initial_ratio = self.calculate_position_size(stock_code, stock_data)
                    self._adjust_position(stock_code, stock_data, initial_ratio)

    def _adjust_position(self, stock_code: str, stock_data: pd.Series, target_ratio: float):
        """调整持仓"""
        current_price = stock_data['close']
        target_value = self.total_value * target_ratio
        
        if stock_code in self.positions:
            current_shares = self.positions[stock_code]['shares']
            current_value = current_shares * current_price
            if target_value > current_value:
                # 加仓
                shares_to_buy = int((target_value - current_value) / current_price)
                self._buy_stock(stock_code, shares_to_buy, current_price, target_ratio)
        else:
            # 新建仓位
            shares_to_buy = int(target_value / current_price)
            self._buy_stock(stock_code, shares_to_buy, current_price, target_ratio)

    def _buy_stock(self, stock_code: str, shares: int, price: float, ratio: float):
        """买入股票"""
        cost = shares * price
        if cost > self.cash:
            shares = int(self.cash / price)
            cost = shares * price
            ratio = cost / self.total_value

        if shares > 0:
            if stock_code in self.positions:
                # 更新现有持仓
                position = self.positions[stock_code]
                total_shares = position['shares'] + shares
                avg_price = (position['shares'] * position['price'] + shares * price) / total_shares
                self.positions[stock_code] = {
                    'shares': total_shares,
                    'price': avg_price,
                    'ratio': ratio
                }
            else:
                # 新建持仓
                self.positions[stock_code] = {
                    'shares': shares,
                    'price': price,
                    'ratio': ratio
                }
            
            self.cash -= cost
            self.last_trade_date[stock_code] = datetime.now()
            print(f"买入 {stock_code}: {shares}股, 价格: {price:.2f}, 仓位: {ratio*100:.1f}%")

    def _sell_stock(self, stock_code: str, shares: int, reason: str):
        """卖出股票"""
        if stock_code in self.positions:
            position = self.positions[stock_code]
            if shares >= position['shares']:
                # 清仓
                self.cash += position['shares'] * position['price']
                print(f"清仓 {stock_code}: {position['shares']}股, 价格: {position['price']:.2f}, 原因: {reason}")
                del self.positions[stock_code]
            else:
                # 部分卖出
                position['shares'] -= shares
                position['ratio'] = position['shares'] * position['price'] / self.total_value
                self.cash += shares * position['price']
                print(f"部分卖出 {stock_code}: {shares}股, 价格: {position['price']:.2f}, 剩余仓位: {position['ratio']*100:.1f}%, 原因: {reason}")

    def run_daily(self, date: str = None):
        """每日运行策略"""
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
            
        print(f"\n=== 运行日期: {date} ===")
        
        # 选股
        selected_sh = self.stock_picker.run(date=date, market='sh')
        selected_sz = self.stock_picker.run(date=date, market='sz')
        
        print(f"\n上证选股数量: {len(selected_sh)}")
        print(f"深证选股数量: {len(selected_sz)}")
        
        # 合并结果
        selected = pd.concat([selected_sh, selected_sz])
        if not selected.empty:
            # 执行交易
            self.execute_trades(selected, date)
            
            # 更新总资产
            self.total_value = self.cash
            for code, pos in self.positions.items():
                stock_data = selected[selected['stock_code'] == code]
                if not stock_data.empty:
                    current_price = stock_data['close'].iloc[0]
                    self.total_value += pos['shares'] * current_price
            
            # 输出当前持仓和资产情况
            print(f"\n当前总资产: {self.total_value:.2f}")
            print(f"现金: {self.cash:.2f}")
            print("\n当前持仓:")
            for code, pos in self.positions.items():
                stock_data = selected[selected['stock_code'] == code]
                if not stock_data.empty:
                    current_price = stock_data['close'].iloc[0]
                    market_value = pos['shares'] * current_price
                    profit_ratio = (current_price - pos['price']) / pos['price'] * 100
                    print(f"{code}: {pos['shares']}股, 成本价: {pos['price']:.2f}, 现价: {current_price:.2f}, "
                          f"市值: {market_value:.2f}, 盈亏: {profit_ratio:.1f}%")
            
            # 等待用户按回车继续
            input("\n按回车键继续...")

def main():
    strategy = IntegratedStrategy2()
    
    # 获取所有交易日
    db = DBUtils()
    trading_days = db.get_trading_days_between("2025-01-01", "2025-05-30")
    
    # 按日期顺序运行策略
    for date in trading_days:
        strategy.run_daily(date)

if __name__ == "__main__":
    main() 