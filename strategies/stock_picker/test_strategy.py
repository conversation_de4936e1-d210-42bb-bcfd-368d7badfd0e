"""
测试策略运行
"""

import sys
import os
import pandas as pd

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from strategies.stock_picker.strategies.position_strategy import PositionStrategy

def test_strategy(date: str):
    """测试策略运行"""
    print(f"\n=== 测试日期: {date} ===")
    
    # 初始化策略
    strategy = PositionStrategy()
    
    # 初始化持仓信息（空持仓）
    positions = {
        'cash': 1000000  # 初始资金100万
    }
    
    # 运行策略
    result = strategy.run(date, positions)
    
    # 打印结果
    if result['buy_signals']:
        print("\n买入信号:")
        for signal in result['buy_signals']:
            print(f"\n股票代码: {signal['stock_code']}")
            print(f"买入价格: {signal['price']:.2f}")
            print(f"买入数量: {signal['shares']}")
            print(f"买入原因: {signal['reason']}")
            print("-" * 50)
    else:
        print("\n没有买入信号")

if __name__ == '__main__':
    test_date = '2025-01-16'
    test_strategy(test_date) 