"""
整合选股策略和分仓交易策略的运行脚本
支持交互式日期推进和交易执行
"""

import sys
import os
print("Python路径:")
print(sys.path)
print("\n当前工作目录:", os.getcwd())
print("\n当前文件位置:", os.path.abspath(__file__))

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if current_dir not in sys.path:
    sys.path.append(current_dir)

import pandas as pd
from datetime import datetime
from typing import Dict, Optional, List
from .strategies.select_strategy_1 import SelectStrategy1
from .strategies.position_strategy import PositionStrategy
from .strategies.market_sentiment_strategy import MarketSentimentStrategy
from ..utils.db_utils import DBUtils
import time
import traceback

class IntegratedStrategy:
    def __init__(self):
        self.stock_picker = SelectStrategy1()
        self.position_strategy = PositionStrategy()
        self.market_strategy = MarketSentimentStrategy()
        self.db = DBUtils()
        self.positions = {}  # 当前持仓
        self.cash = 1000000  # 初始资金100万
        self.total_value = self.cash  # 总资产
        
    def get_next_trading_day(self, current_date: str) -> Optional[str]:
        """获取下一个交易日"""
        conn = self.db.get_connection()
        query = f"""
        SELECT MIN(date) as next_date
        FROM stock_history
        WHERE date > '{current_date}'
        """
        result = pd.read_sql(query, conn)
        return result['next_date'].iloc[0] if not result.empty else None
        
    def calculate_position_value(self) -> float:
        """计算当前持仓市值"""
        position_value = 0
        for code, pos in self.positions.items():
            if isinstance(pos, dict) and 'size' in pos and 'current_price' in pos:
                position_value += pos['current_price'] * pos['size']
        return position_value
        
    def execute_trades(self, signals: Dict, selected_stocks: pd.DataFrame):
        """执行交易"""
        # 处理卖出信号
        for signal in signals['sell_signals']:
            code = signal['stock_code']
            if code in self.positions:
                pos = self.positions[code]
                # 计算卖出数量（向下取整到100股）
                sell_size = int((pos['size'] * signal['position_ratio']) / 100) * 100
                sell_amount = sell_size * signal['price']
                self.cash += sell_amount
                
                if signal['action'] == 'clear' or sell_size >= pos['size']:
                    print(f"清仓 {code} ({pos['stock_name']}): 卖出 {pos['size']} 股，回收资金 {sell_amount:.2f} 元 (原因: {signal['reason']})")
                    del self.positions[code]
                else:
                    # 部分卖出
                    pos['size'] -= sell_size
                    print(f"减仓 {code} ({pos['stock_name']}): 卖出 {sell_size} 股，回收资金 {sell_amount:.2f} 元 (原因: {signal['reason']})")
        
        # 处理买入信号
        total_assets = self.cash + self.calculate_position_value()
        max_position_per_stock = total_assets * 0.15
        
        for signal in signals['buy_signals']:
            code = signal['stock_code']
            
            # 获取股票数据
            stock_data = selected_stocks[selected_stocks['stock_code'] == code]
            if stock_data.empty:
                continue
            stock_data = stock_data.iloc[0]
            
            # 计算可买入数量
            available_cash = min(self.cash, max_position_per_stock)
            max_shares = int(available_cash / signal['price'] / 100) * 100
            if max_shares < 100:  # 如果不够买100股，跳过
                continue
                
            # 计算实际买入成本
            cost = max_shares * signal['price']
            if cost > self.cash:  # 如果现金不足，跳过
                continue
                
            # 更新现金
            self.cash -= cost
            
            if code in self.positions:
                # 加仓
                pos = self.positions[code]
                old_size = pos['size']
                pos['size'] += max_shares
                pos['cost'] = (pos['cost'] * old_size + signal['price'] * max_shares) / pos['size']
                print(f"加仓 {code} ({stock_data['stock_name']}): 买入 {max_shares} 股，花费 {cost:.2f} 元 (原因: {signal['reason']})")
            else:
                # 新建仓
                self.positions[code] = {
                    'size': max_shares,
                    'cost': signal['price'],
                    'max_size': int(max_position_per_stock / signal['price']),  # 最大可持仓数量
                    'current_price': signal['price'],
                    'stock_name': stock_data['stock_name']  # 添加股票名称
                }
                print(f"建仓 {code} ({stock_data['stock_name']}): 买入 {max_shares} 股，花费 {cost:.2f} 元 (原因: {signal['reason']})")
                
        # 更新持仓的当前价格
        for code in list(self.positions.keys()):
            stock_data = selected_stocks[selected_stocks['stock_code'] == code]
            if not stock_data.empty:
                self.positions[code]['current_price'] = stock_data.iloc[0]['close']
        
    def run_daily(self, date: str):
        """运行每日策略
        
        Args:
            date: 交易日期
        """
        print(f"\n=== 运行日期：{date} ===")
        
        # 1. 更新市场情绪
        print("\n1. 分析市场情绪...")
        market_sentiment = self.market_strategy.get_market_sentiment(date)
        total_stocks = market_sentiment['total_stocks']
        up_stocks = market_sentiment['up_stocks']
        down_stocks = market_sentiment['down_stocks']
        up_ratio = market_sentiment['up_ratio']
        sentiment = market_sentiment['sentiment']
        
        print(f"   总股票数: {total_stocks}\t\t上涨家数: {up_stocks}\t\t下跌家数: {down_stocks}\t 上涨占比: {up_ratio:.2f}%\t  市场情绪: {sentiment}\n")
        
        # 2. 运行选股策略
        print("\n2. 运行选股策略...")
        start_time = time.time()
        selected_sh = self.stock_picker.run(date=date, market='sh')
        selected_sz = self.stock_picker.run(date=date, market='sz')
        selected_stocks = pd.concat([selected_sh, selected_sz])
        end_time = time.time()
        
        if not selected_stocks.empty:
            # 保留需要显示的列
            display_columns = [
                'stock_code', 'stock_name', 'close', 'amount', 'amplitude_5d',
                'trend_score', 'volume_score', 'pressure_support_score', 'momentum_score',
                'total_score', 'suggested_buy_price'
            ]
            selected_stocks = selected_stocks[display_columns]
            
            # 按总分排序
            selected_stocks = selected_stocks.sort_values('total_score', ascending=False)
            print(f"\n选股结果：共选出 {len(selected_stocks)} 只股票")
            print(f"展示得分最高的前3只股票：")
            print("\n序号  股票代码    名称    现价    成交额    振幅   趋势  量能  压支  动量   总分  建议买入")
            print("-" * 95)
            
            # 格式化输出每一行，只显示前3只
            for i, row in enumerate(selected_stocks.head(3).itertuples(index=False), 1):
                # 统一股票代码格式
                stock_code = row.stock_code
                if not stock_code.startswith(('sh.', 'sz.')):
                    if stock_code.startswith('6'):
                        stock_code = 'sh.' + stock_code
                    else:
                        stock_code = 'sz.' + stock_code
                amount_str = f"{row.amount/1e8:.2f}亿"
                print(
                    f"{i:2d}  {stock_code:<9s} {row.stock_name:<8s} "
                    f"{row.close:6.2f}  {amount_str:>7s}  "
                    f"{row.amplitude_5d:5.1f}%  "
                    f"{row.trend_score:4.1f} {row.volume_score:4.1f} "
                    f"{row.pressure_support_score:4.1f} {row.momentum_score:4.1f}  "
                    f"{row.total_score:5.1f}  {row.suggested_buy_price:6.2f}"
                )
            
            # 打印评分说明
            print("\n评分说明：")
            print("趋势 - 趋势得分（30分）：均线趋势和价格位置")
            print("量能 - 成交量得分（30分）：成交额排名和成交量稳定性")
            print("压支 - 压力支撑得分（30分）：量价压力和支撑强度")
            print("动量 - 动量指标得分（20分）：突破趋势和回踩支撑")
            print("\n建议买入价说明：")
            print("1. 考虑了量价压力、动量和趋势等多个因素")
            print("2. 建议在实际买入时设置价格区间：建议买入价 ±2%")
            print("3. 建议分批买入，可以在建议价格以下逐步建仓")
        else:
            print("\n没有股票通过选股策略的筛选")
            return
            
        # 3. 更新持仓股票的当前价格
        print("\n3. 更新持仓信息...")
        for code in list(self.positions.keys()):
            # 获取指定日期的股票数据
            conn = self.db.get_connection()
            query = """
            SELECT close 
            FROM stock_history 
            WHERE stock_code = ? AND date = ?
            """
            result = pd.read_sql(query, conn, params=(code, date))
            
            if not result.empty:
                self.positions[code]['current_price'] = result['close'].iloc[0]
            else:
                print(f"警告：无法获取 {code} 在 {date} 的价格数据")
        
        # 4. 运行仓位管理策略
        print("\n4. 运行仓位管理策略...")
        signals = self.position_strategy.run(date, self.positions)
        
        # 5. 执行交易
        print("\n5. 执行交易...")
        self.execute_trades(signals, selected_stocks)
        
        # 6. 输出当日结算
        position_value = self.calculate_position_value()
        total_value = self.cash + position_value
        value_change = total_value - self.total_value
        value_change_pct = (value_change / self.total_value) * 100 if self.total_value > 0 else 0
        self.total_value = total_value
        
        print("\n=== 当日结算 ===")
        print(f"结算日期: {date}")
        print(f"现金余额: {self.cash:,.2f} 元")
        print(f"持仓市值: {position_value:,.2f} 元")
        print(f"总资产: {total_value:,.2f} 元")
        print(f"当日盈亏: {value_change:,.2f} 元 ({value_change_pct:+.2f}%)")
        
        if self.positions:
            print("\n当前持仓:")
            print("\n代码      名称    数量    成本    现价    市值      盈亏")
            print("-" * 75)
            for code, pos in self.positions.items():
                # 跳过非字典类型的持仓（如cash）
                if not isinstance(pos, dict):
                    continue
                market_value = pos['size'] * pos['current_price']
                cost_value = pos['size'] * pos['cost']
                profit = market_value - cost_value
                profit_pct = (profit / cost_value) * 100 if cost_value > 0 else 0
                print(f"{code:<9s} {pos['stock_name']:<6s} {int(pos['size']):6d}  {pos['cost']:6.2f}  {pos['current_price']:6.2f}  "
                      f"{market_value:8,.2f}  {profit:8,.2f} ({profit_pct:+.2f}%)")
        
        print("\n=== 策略运行完成 ===")
        print(f"运行日期: {date}")
        print(f"耗时：{end_time - start_time:.1f}秒")
        
def main():
    # 创建策略实例
    strategy = IntegratedStrategy()
    
    # 设置起始日期
    current_date = "2025-01-02"  # 确保有足够的历史数据
    
    while True:
        # 运行当日策略
        strategy.run_daily(current_date)
        
        # 获取下一个交易日
        next_date = strategy.get_next_trading_day(current_date)
        if not next_date:
            print("\n已到达最新交易日")
            break
            
        # 等待用户按回车继续
        input("\n按回车进入下一个交易日...")
        current_date = next_date

if __name__ == "__main__":
    main() 