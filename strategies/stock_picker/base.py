from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import pandas as pd
from strategies.utils.db_utils import DBUtils

class BaseStockPicker(ABC):
    """选股策略基类"""
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.db = DBUtils()
        
    @abstractmethod
    def pick_stocks(self, date: str, market: Optional[str] = None) -> pd.DataFrame:
        """选股方法，需要在子类中实现
        
        Args:
            date: 选股日期，格式：YYYY-MM-DD
            market: 可选，市场类型 'sh' 或 'sz'
            
        Returns:
            DataFrame: 选中的股票列表，包含股票代码、名称等信息
        """
        pass
    
    def get_stock_pool(self, date: str, market: Optional[str] = None) -> pd.DataFrame:
        """获取当日可选股票池
        
        Args:
            date: 日期，格式：YYYY-MM-DD
            market: 可选，市场类型 'sh' 或 'sz'
            
        Returns:
            DataFrame: 可选股票池
        """
        return self.db.get_stock_data_by_date(date, market)
    
    def save_picks(self, picks: pd.DataFrame, date: str, output_file: Optional[str] = None):
        """保存选股结果
        
        Args:
            picks: 选中的股票DataFrame
            date: 选股日期
            output_file: 可选，输出文件路径
        """
        if output_file:
            picks.to_csv(output_file, index=False, encoding='utf-8')
            print(f"选股结果已保存到：{output_file}")
        else:
            print("\n选股结果：")
            print(picks.to_string())
    
    def run(self, date: Optional[str] = None, market: Optional[str] = None, 
            output_file: Optional[str] = None) -> pd.DataFrame:
        """运行选股策略
        
        Args:
            date: 可选，选股日期，默认为最新交易日
            market: 可选，市场类型 'sh' 或 'sz'
            output_file: 可选，输出文件路径
            
        Returns:
            DataFrame: 选中的股票列表
        """
        if date is None:
            date = self.db.get_latest_trading_date()
            
        print(f"正在运行选股策略：{self.name}")
        print(f"选股日期：{date}")
        if market:
            print(f"市场：{market}")
            
        picks = self.pick_stocks(date, market)
        self.save_picks(picks, date, output_file)
        return picks
