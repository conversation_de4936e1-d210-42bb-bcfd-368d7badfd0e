"""
基于市场情绪和交易日的策略
"""

import pandas as pd
from datetime import datetime
from typing import List, Dict
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
from strategies.utils.db_utils import DBUtils

class MarketSentimentStrategy:
    def __init__(self):
        self.db = DBUtils()
        self._sentiment_cache = {}  # 缓存市场情绪结果
        
    def get_market_sentiment(self, date: str) -> dict:
        """获取市场情绪详细信息，返回字典"""
        # 检查缓存
        if date in self._sentiment_cache:
            return self._sentiment_cache[date]
        
        conn = self.db.get_connection()
        # 获取前一个交易日
        query = f"""
        SELECT MAX(date) as prev_date
        FROM stock_history
        WHERE date < '{date}'
        """
        prev_date = pd.read_sql(query, conn)['prev_date'].iloc[0]
        if not prev_date:
            result = {
                'total_stocks': 0,
                'up_stocks': 0,
                'down_stocks': 0,
                'up_ratio': 50.00,
                'sentiment': 'neutral'
            }
            self._sentiment_cache[date] = result
            return result
        # 查询涨跌家数
        query = f"""
        WITH price_changes AS (
            SELECT 
                stock_code,
                close as today_close,
                LAG(close) OVER (PARTITION BY stock_code ORDER BY date) as prev_close
            FROM stock_history
            WHERE date IN ('{prev_date}', '{date}')
            AND close IS NOT NULL
        )
        SELECT 
            COUNT(CASE WHEN today_close > prev_close THEN 1 END) as rising_count,
            COUNT(CASE WHEN today_close < prev_close THEN 1 END) as falling_count,
            COUNT(*) as total_stocks
        FROM price_changes
        WHERE prev_close IS NOT NULL AND prev_close > 0
        """
        stats = pd.read_sql(query, conn)
        up_stocks = int(stats['rising_count'].iloc[0])
        down_stocks = int(stats['falling_count'].iloc[0])
        total_stocks = int(stats['total_stocks'].iloc[0])
        up_ratio = (up_stocks / total_stocks * 100) if total_stocks > 0 else 50.0
        if up_ratio > 70:
            sentiment = 'bull'
        elif up_ratio < 50:
            sentiment = 'bear'
        else:
            sentiment = 'neutral'
        result = {
            'total_stocks': total_stocks,
            'up_stocks': up_stocks,
            'down_stocks': down_stocks,
            'up_ratio': up_ratio,
            'sentiment': sentiment
        }
        self._sentiment_cache[date] = result
        return result
        
    def get_next_trading_day(self, date: str) -> str:
        """获取下一个交易日"""
        conn = self.db.get_connection()
        query = f"""
        SELECT MIN(date) as next_date
        FROM stock_history
        WHERE date > '{date}'
        """
        result = pd.read_sql(query, conn)
        return result['next_date'].iloc[0] if not result.empty else None
        
    def get_weekday(self, date: str) -> int:
        """获取日期对应的星期几（0-6，0是周一）"""
        return datetime.strptime(date, '%Y-%m-%d').weekday()
        
    def should_buy(self, date: str) -> bool:
        """判断是否应该买入"""
        market_sentiment = self.get_market_sentiment(date)
        if market_sentiment == 'neutral':
            return False
            
        next_trading_day = self.get_next_trading_day(date)
        if not next_trading_day:
            return False
            
        # 只在熊市买入
        return market_sentiment == 'bear'
            
    def should_sell(self, date: str, position_info: Dict = None) -> Dict:
        """判断是否应该卖出
        
        Args:
            date: 当前日期
            position_info: 持仓信息，包含：
                - stock_code: 股票代码
                - buy_price: 买入价格
                - current_price: 当前价格
                - position_size: 持仓数量
                
        Returns:
            Dict: 包含卖出信号和卖出数量的字典
            {
                'should_sell': bool,  # 是否卖出
                'sell_amount': int,   # 卖出数量（如果should_sell为True）
                'reason': str         # 卖出原因
            }
        """
        market_sentiment = self.get_market_sentiment(date)
        
        # 默认返回值
        result = {
            'should_sell': False,
            'sell_amount': 0,
            'reason': ''
        }
        
        # 如果没有持仓信息，直接返回不卖出
        if not position_info:
            return result
            
        # 计算当前收益率
        profit_percent = (position_info['current_price'] - position_info['buy_price']) / position_info['buy_price'] * 100
        
        # 条件1：熊市清仓
        if market_sentiment == 'bear':
            result['should_sell'] = True
            result['sell_amount'] = position_info['position_size']
            result['reason'] = '熊市清仓'
            return result
            
        # 条件2：盈利5%时卖出50%仓位
        if profit_percent >= 5:
            result['should_sell'] = True
            result['sell_amount'] = position_info['position_size'] // 2  # 卖出一半仓位
            result['reason'] = f'盈利达到{profit_percent:.2f}%，卖出50%仓位'
            return result
            
        return result
            
    def run(self, date: str, position_info: Dict = None) -> Dict:
        """运行策略
        
        Args:
            date: 日期
            position_info: 持仓信息（可选）
            
        Returns:
            Dict: 包含市场情绪和交易信号的字典
        """
        # 获取当前日期的星期
        current_date = datetime.strptime(date, '%Y-%m-%d')
        current_weekday = current_date.weekday()
        
        # 判断市场情绪
        market_sentiment = self.get_market_sentiment(date)
        
        # 获取买卖信号
        should_buy_signal = self.should_buy(date)
        sell_signal = self.should_sell(date, position_info)
        
        return {
            'date': date,
            'weekday': current_weekday,
            'market_sentiment': market_sentiment,
            'should_buy': should_buy_signal,
            'should_sell': sell_signal['should_sell'],
            'sell_amount': sell_signal['sell_amount'],
            'sell_reason': sell_signal['reason']
        } 