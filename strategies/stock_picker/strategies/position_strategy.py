"""
仓位管理策略
实现以下交易规则：
1. 买入标的：当日自选股得分排序前3名
2. 买入条件：当天是熊市，并且收盘价没有跌破五日线
3. 买入仓位：每只股票最大可以买总仓位的10%，首次买入该股票所分配的总仓位的50%
4. 清仓条件：收盘价跌破该股票的五日线，卖出该股票全部仓位
5. 最大持股数量：6只
"""

import pandas as pd
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from .market_sentiment_strategy import MarketSentimentStrategy
from .select_strategy_1 import SelectStrategy1
from strategies.utils.db_utils import DBUtils

class PositionStrategy:
    def __init__(self):
        self.db = DBUtils()
        self.market_strategy = MarketSentimentStrategy()
        self.stock_picker = SelectStrategy1()
        self._conn = None  # 添加连接缓存
        
        # 仓位管理配置
        self.INITIAL_POSITION_RATIO = 0.10  # 首次买入占总资金10%
        self.MAX_POSITION_RATIO = 0.20      # 单只股票最大仓位20%
        self.BEAR_MARKET_POSITION_RATIO = 0.15  # 熊市周二/周四买入占总资金15%
        self.BULL_MARKET_REDUCE_RATIO = 0.5  # 牛市周一/周三减仓比例50%

    @property
    def conn(self):
        """获取数据库连接（使用连接池）"""
        if self._conn is None:
            self._conn = self.db.get_connection()
        return self._conn

    def record_trade(self, date: str, stock_code: str, stock_name: str, action: str, 
                    price: float, shares: int, amount: float, reason: str, position_ratio: float):
        """记录交易"""
        try:
            query = """
            INSERT INTO trades (
                date, stock_code, stock_name, action, price, shares, 
                amount, reason, position_ratio
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            self.conn.execute(query, (
                date, stock_code, stock_name, action, price, shares, 
                amount, reason, position_ratio
            ))
            self.conn.commit()
        except Exception as e:
            print(f"记录交易失败: {e}")
            
    def get_ma5(self, stock_code: str, date: str) -> float:
        """获取5日均线"""
        # 移除可能的前缀
        if '.' in stock_code:
            stock_code = stock_code.split('.')[-1]
            
        # 使用缓存的连接
        query = """
        WITH dates AS (
            SELECT DISTINCT date
            FROM stock_history
            WHERE stock_code = ?
            AND date <= ?
            ORDER BY date DESC
            LIMIT 5
        )
        SELECT AVG(close) as ma5
        FROM stock_history
        WHERE stock_code = ?
        AND date IN (SELECT date FROM dates)
        """
        result = pd.read_sql(query, self.conn, params=(stock_code, date, stock_code))
        return result['ma5'].iloc[0] if not result.empty and not pd.isna(result['ma5'].iloc[0]) else None

    def get_stock_data(self, stock_code: str, date: str) -> Optional[pd.Series]:
        """获取指定日期的股票数据"""
        # 移除可能的前缀
        if '.' in stock_code:
            stock_code = stock_code.split('.')[-1]
            
        # 使用缓存的连接
        query = """
        SELECT *
        FROM stock_history
        WHERE stock_code = ? AND date = ?
        """
        result = pd.read_sql(query, self.conn, params=(stock_code, date))
        return result.iloc[0] if not result.empty else None

    def get_top_stocks(self, date: str, top_n: int = 3) -> pd.DataFrame:
        """获取得分最高的前N只股票"""
        # 首先尝试从临时表中获取数据
        query = """
        SELECT *
        FROM stock_selection_results
        WHERE date = ?
        ORDER BY rank
        LIMIT ?
        """
        
        try:
            results = pd.read_sql(query, self.conn, params=(date, top_n))
            if not results.empty:
                return results
        except:
            # 如果临时表不存在或查询失败，使用原始方法
            pass
            
        # 如果临时表中没有数据，使用原始方法
        selected_sh = self.stock_picker.run(date=date, market='sh', print_info=False)
        selected_sz = self.stock_picker.run(date=date, market='sz', print_info=False)
        
        # 合并结果
        selected = pd.concat([selected_sh, selected_sz])
        if selected.empty:
            return pd.DataFrame()
            
        # 按得分排序并取前N名
        selected = selected.sort_values('total_score', ascending=False)
        return selected.head(top_n)

    def get_weekday(self, date: str) -> int:
        """获取指定日期是星期几（0-6表示周一到周日）"""
        return datetime.strptime(date, '%Y-%m-%d').weekday()

    def check_position_adjustment(self, date: str) -> Tuple[str, float, str]:
        """检查是否需要调整仓位
        
        Returns:
            Tuple[str, float, str]: (操作类型, 调整比例, 原因)
            操作类型: 'increase' 增加仓位, 'reduce' 减少仓位, 'none' 不调整
        """
        weekday = self.get_weekday(date)
        market_sentiment = self.market_strategy.get_market_sentiment(date)
        is_bear = market_sentiment.get('sentiment', '') == 'bear'
        is_bull = market_sentiment.get('sentiment', '') == 'bull'
        
        # 熊市周二(1)或周四(3)增加仓位
        if is_bear and weekday in [1, 3]:
            return 'increase', self.BEAR_MARKET_POSITION_RATIO, f"{'周二' if weekday == 1 else '周四'}熊市加仓"
            
        # 牛市周一(0)或周三(2)减少仓位
        if is_bull and weekday in [0, 2]:
            return 'reduce', self.BULL_MARKET_REDUCE_RATIO, f"{'周一' if weekday == 0 else '周三'}牛市减仓"
            
        return 'none', 0, "不调整仓位"

    def check_buy_conditions(self, stock_code: str, date: str) -> Tuple[bool, str]:
        """检查买入条件
        
        Returns:
            (bool, str): (是否满足买入条件, 原因)
        """
        # 获取当日数据
        stock_data = self.get_stock_data(stock_code, date)
        if stock_data is None:
            return False, "无交易数据"
            
        # 获取当日价格
        current_price = stock_data['close']
        
        # 获取5日均线
        ma5 = self.get_ma5(stock_code, date)
        if ma5 is None:
            return False, "无法计算5日均线"
            
        # 不再判断市场情绪，直接判断收盘价是否高于5日线
        if current_price < ma5:
            return False, f"收盘价（{current_price:.2f}）跌破5日线（{ma5:.2f}）"
            
        return True, f"满足买入条件（收盘价{current_price:.2f}高于5日线{ma5:.2f}）"

    def check_clear_position_conditions(self, stock_code: str, date: str, positions: Dict[str, Dict]) -> Tuple[bool, str]:
        """检查清仓条件"""
        # 获取当日数据
        stock_data = self.get_stock_data(stock_code, date)
        if stock_data is None:
            return False, "无交易数据"
            
        # 获取当日价格数据
        current_price = stock_data['close']
        
        # 获取5日均线
        ma5 = self.get_ma5(stock_code, date)
        if ma5 is None:
            return False, "无法计算5日均线"
            
        # 检查收盘价是否跌破5日线
        if current_price < ma5:
            return True, f"收盘价（{current_price:.2f}）跌破5日线（{ma5:.2f}），清仓"
            
        return False, "未满足清仓条件"

    def run(self, date: str, positions: Dict[str, Dict]) -> Dict:
        """运行策略"""
        result = {
            'buy_signals': [],
            'sell_signals': []
        }
        
        # 计算总资产
        total_market_value = sum(p['size'] * p['current_price'] for p in positions.values() if isinstance(p, dict) and 'size' in p and 'current_price' in p)
        cash = positions.get('cash', 0)
        total_assets = total_market_value + cash
        
        # 如果没有设置初始资金，设置默认值100万
        if total_assets == 0:
            total_assets = 1000000
            cash = 1000000
        
        print("\n=== 策略执行详情 ===")
        print(f"当前总资产: {total_assets:.2f}")
        print(f"持仓市值: {total_market_value:.2f}")
        print(f"现金余额: {cash:.2f}")
        
        # 检查是否需要根据市场情绪和星期几调整仓位
        adjust_type, adjust_ratio, adjust_reason = self.check_position_adjustment(date)
        print(f"\n仓位调整检查: {adjust_reason}")
        
        # 1. 检查现有持仓的清仓/减仓信号
        current_holdings = len([k for k, v in positions.items() if isinstance(v, dict) and 'size' in v])
        print(f"\n当前持仓数量: {current_holdings}")
        
        for stock_code in list(positions.keys()):
            if not isinstance(positions[stock_code], dict) or 'size' not in positions[stock_code]:
                continue
                
            # 检查清仓条件
            should_clear, clear_reason = self.check_clear_position_conditions(stock_code, date, positions)
            
            # 如果需要清仓或者是牛市减仓日
            if should_clear:
                result['sell_signals'].append({
                    'stock_code': stock_code,
                    'action': 'clear',
                    'price': positions[stock_code]['current_price'],
                    'position_ratio': 1.0,
                    'reason': clear_reason
                })
                print(f"生成清仓信号: {stock_code}, 原因: {clear_reason}")
                
                # 记录交易
                self.record_trade(
                    date=date,
                    stock_code=stock_code,
                    stock_name=positions[stock_code]['stock_name'],
                    action='clear',
                    price=positions[stock_code]['current_price'],
                    shares=positions[stock_code]['size'],
                    amount=positions[stock_code]['current_price'] * positions[stock_code]['size'],
                    reason=clear_reason,
                    position_ratio=1.0
                )
            elif adjust_type == 'reduce':
                # 牛市周一/周三减仓
                reduce_shares = int(positions[stock_code]['size'] * adjust_ratio / 100) * 100
                if reduce_shares >= 100:
                    result['sell_signals'].append({
                        'stock_code': stock_code,
                        'action': 'reduce',
                        'price': positions[stock_code]['current_price'],
                        'shares': reduce_shares,
                        'position_ratio': adjust_ratio,
                        'reason': adjust_reason
                    })
                    print(f"生成减仓信号: {stock_code}, 减持{reduce_shares}股, 原因: {adjust_reason}")
                    
                    # 记录交易
                    self.record_trade(
                        date=date,
                        stock_code=stock_code,
                        stock_name=positions[stock_code]['stock_name'],
                        action='reduce',
                        price=positions[stock_code]['current_price'],
                        shares=reduce_shares,
                        amount=positions[stock_code]['current_price'] * reduce_shares,
                        reason=adjust_reason,
                        position_ratio=adjust_ratio
                    )
        
        # 2. 检查是否可以买入新股票或加仓
        if current_holdings < 6:  # 最多持有6只股票
            # 获取得分最高的前3只股票
            print("\n获取候选股票...")
            top_stocks = self.get_top_stocks(date)
            print(f"候选股票数量: {len(top_stocks) if not top_stocks.empty else 0}")
            
            for _, stock in top_stocks.iterrows():
                stock_code = stock['stock_code']
                stock_data = self.get_stock_data(stock_code, date)
                if stock_data is None:
                    continue
                    
                # 检查是否已持仓
                is_holding = stock_code in positions
                
                # 检查买入条件
                print(f"\n检查股票{stock_code}的买入条件:")
                should_buy, buy_reason = self.check_buy_conditions(stock_code, date)
                print(f"买入条件检查结果: {buy_reason}")
                
                if should_buy:
                    # 根据市场情绪和星期几决定买入比例
                    position_ratio = self.BEAR_MARKET_POSITION_RATIO if adjust_type == 'increase' else self.INITIAL_POSITION_RATIO
                    
                    # 如果已持仓且是熊市加仓日，可以追加仓位
                    if is_holding and adjust_type == 'increase':
                        current_position = positions[stock_code]['size'] * positions[stock_code]['current_price']
                        max_additional = total_assets * self.MAX_POSITION_RATIO - current_position
                        if max_additional > 0:
                            buy_amount = min(max_additional, total_assets * position_ratio)
                            shares = int(buy_amount / stock_data['close'] / 100) * 100
                            
                            if shares >= 100:
                                result['buy_signals'].append({
                                    'stock_code': stock_code,
                                    'action': 'increase',
                                    'price': stock_data['close'],
                                    'shares': shares,
                                    'reason': f"{buy_reason}，{adjust_reason}",
                                    'position_ratio': position_ratio
                                })
                                print(f"生成加仓信号: {stock_code}, {shares}股")
                                
                                # 记录交易
                                self.record_trade(
                                    date=date,
                                    stock_code=stock_code,
                                    stock_name=stock_data['stock_name'],
                                    action='increase',
                                    price=stock_data['close'],
                                    shares=shares,
                                    amount=stock_data['close'] * shares,
                                    reason=f"{buy_reason}，{adjust_reason}",
                                    position_ratio=position_ratio
                                )
                    
                    # 如果未持仓，可以新建仓位
                    elif not is_holding:
                        buy_amount = total_assets * position_ratio
                        shares = int(buy_amount / stock_data['close'] / 100) * 100
                        
                        if shares >= 100:
                            result['buy_signals'].append({
                                'stock_code': stock_code,
                                'action': 'buy',
                                'price': stock_data['close'],
                                'shares': shares,
                                'reason': f"{buy_reason}，{adjust_reason if adjust_type == 'increase' else '首次建仓'}",
                                'position_ratio': position_ratio
                            })
                            print(f"生成买入信号: {stock_code}, {shares}股")
                            
                            # 记录交易
                            self.record_trade(
                                date=date,
                                stock_code=stock_code,
                                stock_name=stock_data['stock_name'],
                                action='buy',
                                price=stock_data['close'],
                                shares=shares,
                                amount=stock_data['close'] * shares,
                                reason=f"{buy_reason}，{adjust_reason if adjust_type == 'increase' else '首次建仓'}",
                                position_ratio=position_ratio
                            )
                    
                    # 如果买入信号数量达到持仓上限，结束检查
                    if len(result['buy_signals']) + current_holdings >= 6:
                        print("\n达到最大持仓数量，停止选股")
                        break
        
        print(f"\n本次策略运行结果:")
        print(f"买入信号数量: {len(result['buy_signals'])}")
        print(f"卖出信号数量: {len(result['sell_signals'])}")
        
        return result 