"""
交互式策略调试器
"""

import pandas as pd
from datetime import datetime
from typing import List, Dict
from .market_sentiment_strategy import MarketSentimentStrategy
from .select_strategy_1 import SelectStrategy1
from ...utils.db_utils import DBUtils

class StrategyDebugger:
    def __init__(self):
        self.strategy = MarketSentimentStrategy()
        self.stock_picker = SelectStrategy1()
        self.db = DBUtils()
        self.selected_stocks = None
        self.holding = False
        self.buy_date = None
        self.portfolio = None
        
    def get_selected_stocks(self, date: str) -> pd.DataFrame:
        """获取选股策略选出的股票"""
        # 运行选股策略
        selected_sh = self.stock_picker.run(date=date, market='sh')
        selected_sz = self.stock_picker.run(date=date, market='sz')
        
        # 合并结果
        selected = pd.concat([selected_sh, selected_sz])
        if not selected.empty:
            selected = selected[['stock_code', 'stock_name', 'close', 'total_score']]
            selected = selected.sort_values('total_score', ascending=False)
            selected = selected.head(10)  # 只取评分最高的10只股票
            
        return selected

    def get_weekday_name(self, weekday: int) -> str:
        """获取星期几的中文名称"""
        weekday_names = {
            0: '一',
            1: '二',
            2: '三',
            3: '四',
            4: '五',
            5: '六',
            6: '日'
        }
        return weekday_names.get(weekday, '未知')

    def debug_step(self, date: str):
        """执行单日调试"""
        print(f"\n{'='*80}")
        print(f"日期: {date}")
        print(f"{'='*80}")
        
        # 第一步：选股
        print("\n1. 选股结果:")
        print("-" * 40)
        selected_stocks = self.get_selected_stocks(date)
        if selected_stocks.empty:
            print("没有选出任何股票")
        else:
            print("代码      名称        收盘价    评分")
            print("-" * 40)
            for _, stock in selected_stocks.iterrows():
                print(f"{stock['stock_code']}  {stock['stock_name']:<8}  {stock['close']:>8.2f}  {stock['total_score']:>8.2f}")
        
        # 第二步：市场情绪判断
        print("\n2. 市场情绪判断:")
        print("-" * 40)
        result = self.strategy.run(date)
        market_status = {
            'bull': '牛市',
            'bear': '熊市',
            'neutral': '平市'
        }[result['market_sentiment']]
        
        # 获取当前日期的星期
        current_date = datetime.strptime(date, '%Y-%m-%d')
        current_weekday = current_date.weekday()
        
        print(f"当前是星期{self.get_weekday_name(current_weekday)}")
        print(f"\n市场状态: {market_status}")
        
        # 根据市场状态决定买卖信号
        should_buy = market_status == '熊市' and not self.holding
        should_sell = market_status == '牛市' and self.holding
        
        print(f"买入信号: {'是' if should_buy else '否'}")
        print(f"卖出信号: {'是' if should_sell else '否'}")
        
        # 打印交易条件分析
        print("\n交易条件分析:")
        print("-" * 40)
        
        # 分析买入条件
        if not self.holding:
            print("买入条件检查:")
            conditions_met = []
            conditions_not_met = []
            
            if market_status == '平市':
                conditions_not_met.append("当前是平市，不进行交易")
            elif market_status == '熊市':
                conditions_met.append("当前是熊市（涨跌比<0.5），适合买入")
            elif market_status == '牛市':
                conditions_not_met.append("当前是牛市（涨跌比>1.5），不适合买入")
            
            # 打印满足的条件
            if conditions_met:
                print("\n满足的条件:")
                for condition in conditions_met:
                    print(f"√ {condition}")
            
            # 打印不满足的条件
            if conditions_not_met:
                print("\n不满足的条件:")
                for condition in conditions_not_met:
                    print(f"× {condition}")
            
            # 打印结论
            print("\n结论：", end="")
            if conditions_not_met:
                print("不满足买入条件")
            else:
                print("满足所有买入条件")
        
        # 分析卖出条件
        else:
            print("卖出条件检查:")
            conditions_met = []
            conditions_not_met = []
            
            if market_status == '平市':
                conditions_not_met.append("当前是平市，不进行交易")
            elif market_status == '牛市':
                conditions_met.append("当前是牛市（涨跌比>1.5），适合卖出")
            elif market_status == '熊市':
                conditions_not_met.append("当前是熊市（涨跌比<0.5），不适合卖出")
            
            # 打印满足的条件
            if conditions_met:
                print("\n满足的条件:")
                for condition in conditions_met:
                    print(f"√ {condition}")
            
            # 打印不满足的条件
            if conditions_not_met:
                print("\n不满足的条件:")
                for condition in conditions_not_met:
                    print(f"× {condition}")
            
            # 打印结论
            print("\n结论：", end="")
            if conditions_not_met:
                print("不满足卖出条件")
            else:
                print("满足所有卖出条件")
        
        # 第三步：交易执行
        print("\n3. 交易执行:")
        print("-" * 40)
        
        if not self.holding and should_buy:
            self.holding = True
            self.buy_date = date
            self.portfolio = selected_stocks
            print("执行买入操作:")
            print("\n买入的股票组合:")
            for _, stock in selected_stocks.iterrows():
                print(f"买入 {stock['stock_code']} {stock['stock_name']} 价格: {stock['close']:.2f}")
                
        elif self.holding and should_sell:
            print("执行卖出操作:")
            total_return = 0
            print("\n卖出详情:")
            
            for _, buy_stock in self.portfolio.iterrows():
                # 获取卖出价格
                sell_query = f"""
                SELECT close 
                FROM stock_history 
                WHERE stock_code = '{buy_stock['stock_code']}'
                AND date = '{date}'
                """
                conn = self.db.get_connection()
                sell_price_df = pd.read_sql(sell_query, conn)
                if not sell_price_df.empty:
                    sell_price = sell_price_df['close'].iloc[0]
                    stock_return = (sell_price - buy_stock['close']) / buy_stock['close'] * 100
                    total_return += stock_return / len(self.portfolio)
                    print(f"卖出 {buy_stock['stock_code']} {buy_stock['stock_name']}")
                    print(f"  买入价: {buy_stock['close']:.2f}")
                    print(f"  卖出价: {sell_price:.2f}")
                    print(f"  收益率: {stock_return:.2f}%")
            
            print(f"\n组合总收益率: {total_return:.2f}%")
            self.holding = False
            self.buy_date = None
            self.portfolio = None
        
        else:
            if self.holding:
                # 计算当前持仓收益
                total_return = 0
                print("当前持仓中，等待卖出信号")
                print("\n当前持仓收益:")
                
                for _, buy_stock in self.portfolio.iterrows():
                    # 获取当前价格
                    current_query = f"""
                    SELECT close 
                    FROM stock_history 
                    WHERE stock_code = '{buy_stock['stock_code']}'
                    AND date = '{date}'
                    """
                    conn = self.db.get_connection()
                    current_price_df = pd.read_sql(current_query, conn)
                    if not current_price_df.empty:
                        current_price = current_price_df['close'].iloc[0]
                        stock_return = (current_price - buy_stock['close']) / buy_stock['close'] * 100
                        total_return += stock_return / len(self.portfolio)
                        print(f"{buy_stock['stock_code']} {buy_stock['stock_name']}:")
                        print(f"  买入价: {buy_stock['close']:.2f}")
                        print(f"  当前价: {current_price:.2f}")
                        print(f"  收益率: {stock_return:.2f}%")
                
                print(f"\n组合总收益率: {total_return:.2f}%")
            else:
                print("当前空仓，等待买入信号")
        
        input("\n按回车键继续到下一天...")

    def run_debug(self, start_date: str):
        """开始交互式调试"""
        # 获取从开始日期往后的所有交易日
        conn = self.db.get_connection()
        query = f"""
        SELECT DISTINCT date 
        FROM stock_history 
        WHERE date >= '{start_date}'
        ORDER BY date
        """
        dates = pd.read_sql(query, conn)['date'].tolist()
        
        for date in dates:
            self.debug_step(date) 

if __name__ == "__main__":
    debugger = StrategyDebugger()
    debugger.run_debug('2025-03-04')  # 使用测试日期运行调试器 