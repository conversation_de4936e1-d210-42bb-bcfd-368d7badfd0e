"""
市场情绪策略评估器
"""

import pandas as pd
from datetime import datetime
from typing import List, Dict
from strategies.stock_picker.strategies.market_sentiment_strategy import MarketSentimentStrategy
from strategies.stock_picker.strategies.select_strategy_1 import SelectStrategy1
from strategies.utils.db_utils import DBUtils

class MarketSentimentEvaluator:
    def __init__(self):
        self.strategy = MarketSentimentStrategy()
        self.stock_picker = SelectStrategy1()
        self.db = DBUtils()
        self.selected_stocks = None
        
    def get_selected_stocks(self, date: str) -> pd.DataFrame:
        """获取选股策略选出的股票"""
        if self.selected_stocks is None or date not in self.selected_stocks:
            # 运行选股策略
            selected_sh = self.stock_picker.run(date=date, market='sh')
            selected_sz = self.stock_picker.run(date=date, market='sz')
            
            # 合并结果
            selected = pd.concat([selected_sh, selected_sz])
            if not selected.empty:
                selected = selected[['stock_code', 'stock_name', 'close', 'total_score']]
                selected = selected.sort_values('total_score', ascending=False)
                selected = selected.head(10)  # 只取评分最高的10只股票
                
            self.selected_stocks = {date: selected}
            
        return self.selected_stocks[date]
        
    def calculate_returns(self, buy_date: str, sell_date: str) -> Dict:
        """计算从买入日到卖出日的选股组合收益率"""
        # 获取买入日选出的股票
        selected_stocks = self.get_selected_stocks(buy_date)
        if selected_stocks.empty:
            print(f"警告：{buy_date} 没有选出任何股票")
            return None
            
        # 计算每只股票的收益率
        total_return = 0
        buy_prices = []
        sell_prices = []
        
        conn = self.db.get_connection()
        for _, stock in selected_stocks.iterrows():
            stock_code = stock['stock_code']
            
            # 获取买入价格
            buy_query = f"""
            SELECT close 
            FROM stock_history 
            WHERE stock_code = '{stock_code}'
            AND date = '{buy_date}'
            """
            buy_price_df = pd.read_sql(buy_query, conn)
            if buy_price_df.empty:
                continue
            buy_price = buy_price_df['close'].iloc[0]
            
            # 获取卖出价格
            sell_query = f"""
            SELECT close 
            FROM stock_history 
            WHERE stock_code = '{stock_code}'
            AND date = '{sell_date}'
            """
            sell_price_df = pd.read_sql(sell_query, conn)
            if sell_price_df.empty:
                continue
            sell_price = sell_price_df['close'].iloc[0]
            
            # 计算收益率并累加（等权重）
            stock_return = (sell_price - buy_price) / buy_price * 100
            total_return += stock_return / len(selected_stocks)
            
            buy_prices.append(buy_price)
            sell_prices.append(sell_price)
            
        if not buy_prices:  # 如果没有任何有效的交易
            return None
            
        # 计算平均买入和卖出价格
        avg_buy_price = sum(buy_prices) / len(buy_prices)
        avg_sell_price = sum(sell_prices) / len(sell_prices)
        
        # 打印调试信息
        print(f"   买入日期: {buy_date}, 平均价格: {avg_buy_price:.2f}")
        print(f"   卖出日期: {sell_date}, 平均价格: {avg_sell_price:.2f}")
        
        return {
            'return': total_return,
            'buy_price': avg_buy_price,
            'sell_price': avg_sell_price
        }
        
    def evaluate_strategy(self, start_date: str, end_date: str):
        """评估策略在指定时间段的表现"""
        # 获取时间段内的所有交易日
        conn = self.db.get_connection()
        query = f"""
        SELECT DISTINCT date 
        FROM stock_history 
        WHERE date >= '{start_date}' AND date <= '{end_date}'
        ORDER BY date
        """
        dates = pd.read_sql(query, conn)['date'].tolist()
        
        # 存储交易记录
        trades = []
        holding = False
        buy_date = None
        
        print(f"\n开始评估从 {start_date} 到 {end_date} 的策略表现")
        print("=" * 60)
        
        for date in dates:
            result = self.strategy.run(date)
            next_day_weekday = result['next_trading_day_weekday'] + 1 if result['next_trading_day_weekday'] is not None else None
            
            # 打印每个交易日的状态
            print(f"\n日期: {date}")
            market_status = {
                'bull': '牛市',
                'bear': '熊市',
                'neutral': '平市'
            }[result['market_sentiment']]
            print(f"市场状态: {market_status}")
            print(f"下一交易日: {result['next_trading_day']} (周{next_day_weekday})")
            print(f"当前持仓: {'是' if holding else '否'}")
            print(f"买入信号: {'是' if result['should_buy'] else '否'}")
            print(f"卖出信号: {'是' if result['should_sell'] else '否'}")
            
            if not holding and result['should_buy']:
                # 买入
                holding = True
                buy_date = date
                print("=> 执行买入")
                # 打印选出的股票
                selected_stocks = self.get_selected_stocks(date)
                print("\n选出的股票：")
                for _, stock in selected_stocks.iterrows():
                    print(f"   {stock['stock_code']} {stock['stock_name']} 评分：{stock['total_score']:.2f}")
                
            elif holding and result['should_sell']:
                # 卖出
                returns = self.calculate_returns(buy_date, date)
                if returns:
                    trade = {
                        'buy_date': buy_date,
                        'sell_date': date,
                        'holding_days': len(self.db.get_trading_days_between(buy_date, date)),
                        'return': returns['return'],
                        'buy_price': returns['buy_price'],
                        'sell_price': returns['sell_price'],
                        'market_condition': market_status
                    }
                    trades.append(trade)
                    print("=> 执行卖出")
                    print(f"   持仓天数: {trade['holding_days']}天")
                    print(f"   收益率: {trade['return']:.2f}%")
                    
                holding = False
                buy_date = None
                
        # 计算整体统计
        if trades:
            trades_df = pd.DataFrame(trades)
            
            print("\n" + "=" * 60)
            print("\n整体表现统计：")
            
            # 计算胜率
            wins = len(trades_df[trades_df['return'] > 0])
            total_trades = len(trades_df)
            win_rate = wins / total_trades * 100
            
            print(f"\n总交易次数: {total_trades}")
            print(f"胜率: {win_rate:.2f}%")
            print(f"平均收益率: {trades_df['return'].mean():.2f}%")
            print(f"最大收益率: {trades_df['return'].max():.2f}%")
            print(f"最小收益率: {trades_df['return'].min():.2f}%")
            print(f"平均持仓天数: {trades_df['holding_days'].mean():.1f}天")
            
            # 按市场状态分组统计
            print("\n市场状态分析：")
            for market in ['牛市', '熊市', '平市']:
                market_trades = trades_df[trades_df['market_condition'] == market]
                if len(market_trades) > 0:
                    market_wins = len(market_trades[market_trades['return'] > 0])
                    market_win_rate = market_wins / len(market_trades) * 100
                    print(f"\n{market}:")
                    print(f"交易次数: {len(market_trades)}")
                    print(f"胜率: {market_win_rate:.2f}%")
                    print(f"平均收益率: {market_trades['return'].mean():.2f}%")
            
        return trades

if __name__ == '__main__':
    # 创建评估器实例
    evaluator = MarketSentimentEvaluator()
    
    # 评估3月1日到5月23日的表现
    results = evaluator.evaluate_strategy('2025-03-01', '2025-05-23') 