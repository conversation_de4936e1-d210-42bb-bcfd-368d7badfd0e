"""
策略评估器，用于评估选股策略的表现
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from strategies.stock_picker.strategies.select_strategy_1 import SelectStrategy1
from strategies.utils.db_utils import DBUtils

class StrategyEvaluator:
    def __init__(self):
        self.strategy = SelectStrategy1()
        self.db = DBUtils()
        
    def get_next_n_trading_days(self, start_date: str, n: int) -> List[str]:
        """获取从start_date开始的n个交易日
        
        Args:
            start_date: 开始日期
            n: 需要获取的交易日数量
            
        Returns:
            n个交易日的列表
        """
        conn = self.db.get_connection()
        query = f"""
        SELECT DISTINCT date 
        FROM stock_history 
        WHERE date > '{start_date}'
        ORDER BY date
        LIMIT {n}
        """
        dates = pd.read_sql(query, conn)
        return dates['date'].tolist()
        
    def calculate_returns(self, stock_code: str, buy_date: str, future_dates: List[str]) -> Dict[int, float]:
        """计算股票在未来日期的收益率
        
        Args:
            stock_code: 股票代码
            buy_date: 买入日期
            future_dates: 未来日期列表
            
        Returns:
            各期收益率字典
        """
        # 获取买入价格
        hist_data = self.db.get_daily_data(stock_code, end_date=buy_date)
        if hist_data.empty:
            return {}
        buy_price = hist_data.iloc[-1]['close']
        
        # 获取未来价格
        returns = {}
        for i, future_date in enumerate(future_dates, 1):
            future_data = self.db.get_daily_data(stock_code, end_date=future_date)
            if not future_data.empty:
                future_price = future_data.iloc[-1]['close']
                returns[i] = (future_price - buy_price) / buy_price * 100
                
        return returns
        
    def evaluate_strategy(self, date: str, top_n: int = 10) -> pd.DataFrame:
        """评估策略在指定日期的表现
        
        Args:
            date: 选股日期
            top_n: 选取得分最高的前N只股票
            
        Returns:
            评估结果DataFrame
        """
        # 获取选股结果
        selected_stocks = self.strategy.run(date=date)
        if selected_stocks.empty:
            print(f"日期 {date} 没有选出股票")
            return pd.DataFrame()
            
        # 获取未来10个交易日
        future_dates = self.get_next_n_trading_days(date, 10)
        if len(future_dates) < 10:
            print(f"警告：从 {date} 开始只能获取到 {len(future_dates)} 个交易日的数据")
            
        # 选取得分最高的前N只股票
        top_stocks = selected_stocks.nlargest(top_n, 'total_score')
        print(f"\n得分最高的{top_n}只股票：")
        for _, stock in top_stocks.iterrows():
            print(f"{stock['stock_code']} {stock['stock_name']}: {stock['total_score']:.2f}分")
            
        # 计算每只股票的收益率
        results = []
        portfolio_value = 1000000  # 100万初始资金
        per_stock_value = portfolio_value / top_n  # 每只股票分配资金
        
        for _, stock in top_stocks.iterrows():
            stock_code = stock['stock_code']
            returns = self.calculate_returns(stock_code, date, future_dates)
            
            # 计算持仓数量
            buy_price = self.db.get_daily_data(stock_code, end_date=date).iloc[-1]['close']
            shares = int(per_stock_value / buy_price)  # 向下取整，模拟实际可买入数量
            actual_value = shares * buy_price
            
            result = {
                'stock_code': stock_code,
                'name': stock['stock_name'],
                'score': stock['total_score'],
                'buy_price': buy_price,
                'shares': shares,
                'position_value': actual_value
            }
            
            # 添加5日、10日收益率
            for days in [5, 10]:
                if days <= len(returns):
                    result[f'{days}日收益率'] = returns[days]
                    result[f'{days}日市值'] = actual_value * (1 + returns[days]/100)
                else:
                    result[f'{days}日收益率'] = None
                    result[f'{days}日市值'] = None
                    
            results.append(result)
            
        # 转换为DataFrame
        results_df = pd.DataFrame(results)
        
        # 计算组合总市值和收益率
        initial_portfolio = results_df['position_value'].sum()
        print(f"\n实际使用资金：{initial_portfolio:.2f}元")
        
        # 计算胜率和组合收益
        print("\n组合表现：")
        for days in [5, 10]:
            if f'{days}日市值' in results_df.columns:
                # 计算个股胜率
                total = len(results_df[results_df[f'{days}日收益率'].notna()])
                if total > 0:
                    wins = len(results_df[results_df[f'{days}日收益率'] > 0])
                    win_rate = wins / total * 100
                    print(f"{days}日胜率: {win_rate:.2f}%")
                
                # 计算组合收益率
                final_portfolio = results_df[f'{days}日市值'].sum()
                portfolio_return = (final_portfolio - initial_portfolio) / initial_portfolio * 100
                print(f"{days}日组合收益率: {portfolio_return:.2f}%")
                
                # 计算个股平均收益率
                avg_return = results_df[f'{days}日收益率'].mean()
                print(f"{days}日平均收益率: {avg_return:.2f}%")
                print()
                
        return results_df

    def evaluate_multiple_dates(self, start_date: str, end_date: str):
        """评估一段时间内的策略表现
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
        """
        # 获取这段时间内的所有交易日
        conn = self.db.get_connection()
        query = f"""
        SELECT DISTINCT date 
        FROM stock_history 
        WHERE date >= '{start_date}' AND date <= '{end_date}'
        ORDER BY date
        """
        dates = pd.read_sql(query, conn)['date'].tolist()
        
        # 存储每个交易日的评估结果
        all_results = []
        
        print(f"\n开始评估从 {start_date} 到 {end_date} 的策略表现")
        print("=" * 60)
        
        for date in dates:
            try:
                # 获取选股结果
                selected_stocks = self.strategy.run(date=date)
                if selected_stocks.empty:
                    print(f"\n{date} 没有选出股票")
                    continue
                    
                # 获取未来10个交易日
                future_dates = self.get_next_n_trading_days(date, 10)
                if len(future_dates) < 5:  # 至少需要5个交易日的数据
                    print(f"\n{date} 后续数据不足")
                    continue
                    
                # 选取得分最高的前10只股票
                top_stocks = selected_stocks.nlargest(10, 'total_score')
                
                # 计算每只股票的收益率
                portfolio_value = 1000000  # 100万初始资金
                per_stock_value = portfolio_value / 10  # 每只股票分配资金
                
                stock_results = []
                for _, stock in top_stocks.iterrows():
                    stock_code = stock['stock_code']
                    returns = self.calculate_returns(stock_code, date, future_dates)
                    
                    # 计算持仓数量
                    buy_price = self.db.get_daily_data(stock_code, end_date=date).iloc[-1]['close']
                    shares = int(per_stock_value / buy_price)
                    actual_value = shares * buy_price
                    
                    for days in [5, 10]:
                        if days <= len(returns):
                            stock_results.append({
                                'date': date,
                                'stock_code': stock_code,
                                'name': stock['stock_name'],
                                'score': stock['total_score'],
                                'days': days,
                                'return': returns[days],
                                'position_value': actual_value
                            })
                
                if stock_results:
                    results_df = pd.DataFrame(stock_results)
                    
                    # 计算当日表现
                    day_stats = {}
                    for days in [5, 10]:
                        day_data = results_df[results_df['days'] == days]
                        if not day_data.empty:
                            wins = len(day_data[day_data['return'] > 0])
                            total = len(day_data)
                            win_rate = wins / total * 100
                            avg_return = day_data['return'].mean()
                            
                            # 计算组合收益率
                            initial_value = day_data['position_value'].sum()
                            final_value = sum(value * (1 + ret/100) for value, ret in zip(day_data['position_value'], day_data['return']))
                            portfolio_return = (final_value - initial_value) / initial_value * 100
                            
                            day_stats[days] = {
                                'date': date,
                                'win_rate': win_rate,
                                'avg_return': avg_return,
                                'portfolio_return': portfolio_return
                            }
                    
                    all_results.append(day_stats)
                    
                    print(f"\n日期：{date}")
                    print(f"5日胜率: {day_stats[5]['win_rate']:.2f}% 组合收益率: {day_stats[5]['portfolio_return']:.2f}%")
                    if 10 in day_stats:
                        print(f"10日胜率: {day_stats[10]['win_rate']:.2f}% 组合收益率: {day_stats[10]['portfolio_return']:.2f}%")
                    
            except Exception as e:
                print(f"\n处理日期 {date} 时发生错误: {str(e)}")
                continue
        
        # 计算整体统计
        if all_results:
            print("\n" + "=" * 60)
            print("\n整体表现统计：")
            
            # 5日统计
            win_rates_5d = [r[5]['win_rate'] for r in all_results]
            returns_5d = [r[5]['portfolio_return'] for r in all_results]
            print("\n5日表现：")
            print(f"平均胜率: {sum(win_rates_5d)/len(win_rates_5d):.2f}%")
            print(f"平均收益率: {sum(returns_5d)/len(returns_5d):.2f}%")
            print(f"最大收益率: {max(returns_5d):.2f}%")
            print(f"最小收益率: {min(returns_5d):.2f}%")
            
            # 10日统计
            win_rates_10d = [r[10]['win_rate'] for r in all_results if 10 in r]
            returns_10d = [r[10]['portfolio_return'] for r in all_results if 10 in r]
            if win_rates_10d:
                print("\n10日表现：")
                print(f"平均胜率: {sum(win_rates_10d)/len(win_rates_10d):.2f}%")
                print(f"平均收益率: {sum(returns_10d)/len(returns_10d):.2f}%")
                print(f"最大收益率: {max(returns_10d):.2f}%")
                print(f"最小收益率: {min(returns_10d):.2f}%")
        
        return all_results

if __name__ == '__main__':
    # 创建评估器实例
    evaluator = StrategyEvaluator()
    
    # 评估3月1日到5月23日的表现
    results = evaluator.evaluate_multiple_dates('2025-03-01', '2025-05-23') 