"""
这是第一套选股策略，选股条件分为粗排和精排：
粗排阶段规则：从本地数据库取出所有股票，筛选：
1. 成交额大于3亿
2. 过去5日内振幅<15%（使用amplitude_5d字段）
3. 5元 < 当前价格 < 200元
4. 最近5日内无涨停跌停
5. MA5 > MA10（短期上升趋势）且当前价格 > MA5（股价在均线上方）
6. 当日成交量在5日平均成交量的0.5-2倍之间（使用volume_ratio_5d字段）
7. 排除ST股票和新股（上市时间少于3个月）
8. 最近3个交易日不能连续下跌
9. 日内波动率不能超过5%

精排阶段规则：对通过粗排的股票进行评分（总分130分）：
1. 趋势维度（30分）：
   - MA5与MA10的差距百分比（15分）
   - 当前价格相对MA5的位置（15分）
2. 成交量维度（30分）：
   - 成交额排名（15分）
   - 成交量趋势（15分）
3. 波动维度（20分）：
   - 振幅得分（10分）
   - 日内波动率（10分）
4. 压力支撑维度（30分）：
   - 压力指标得分（15分）
   - 支撑指标得分（15分）
5. 动量维度（20分）：
   - 突破趋势（10分）
   - 回踩支撑（10分）
"""

import pandas as pd
import numpy as np
from typing import Optional
from strategies.stock_picker.base import BaseStockPicker

class SelectStrategy1(BaseStockPicker):
    def __init__(self):
        super().__init__(
            name="选股策略一",
            description="基于多个技术指标的选股策略"
        )
        self._data_cache = {}  # 添加数据缓存
        
    def _get_cached_data(self, date: str, market: Optional[str] = None) -> Optional[pd.DataFrame]:
        """从缓存获取数据"""
        cache_key = f"{date}_{market}"
        return self._data_cache.get(cache_key)
        
    def _cache_data(self, date: str, market: Optional[str], data: pd.DataFrame):
        """缓存数据"""
        cache_key = f"{date}_{market}"
        self._data_cache[cache_key] = data
        
    def calculate_amplitude(self, data: pd.DataFrame) -> float:
        """计算N日振幅
        
        Args:
            data: 股票数据
            
        Returns:
            float: 振幅百分比 = (N日内最高价 - N日内最低价) / N日内最低价 * 100%
        """
        max_price = data['high'].max()
        min_price = data['low'].min()
        return (max_price - min_price) / min_price * 100

    def has_limit_up_down(self, data: pd.DataFrame) -> bool:
        """检查是否有涨停或跌停
        
        Args:
            data: 股票数据
            
        Returns:
            bool: 是否有涨停或跌停
        """
        # 创建副本以避免SettingWithCopy警告
        data_copy = data.copy()
        # 计算每日涨跌幅
        data_copy.loc[:, 'pct_change'] = data_copy['close'].pct_change() * 100
        # 涨停约为10%，跌停约为-10%（考虑一些误差）
        return any((data_copy['pct_change'].abs() > 9.5))

    def calculate_ma_trend(self, data: pd.DataFrame) -> bool:
        """计算均线趋势
        
        Args:
            data: 股票数据
            
        Returns:
            bool: MA5是否大于MA10
        """
        # 计算MA5和MA10
        ma5 = data['close'].rolling(window=5).mean().iloc[-1]
        ma10 = data['close'].rolling(window=10).mean().iloc[-1]
        return ma5 > ma10

    def check_volume_stability(self, data: pd.DataFrame) -> bool:
        """检查成交量稳定性
        
        Args:
            data: 股票数据
            
        Returns:
            bool: 当日成交量是否在合理范围内
        """
        # 计算5日平均成交量
        avg_volume = data['volume'].rolling(window=5).mean().iloc[-1]
        latest_volume = data['volume'].iloc[-1]
        # 检查是否在0.5-2倍范围内
        return 0.5 <= (latest_volume / avg_volume) <= 2.0

    def calculate_trend_score(self, data: pd.DataFrame, ma5: float, ma10: float) -> float:
        """计算趋势得分（30分）
        
        Args:
            data: 股票数据
            ma5: 5日均线
            ma10: 10日均线
            
        Returns:
            float: 趋势得分
        """
        # 计算MA5与MA10的差距得分（15分）
        ma_diff_pct = (ma5 - ma10) / ma10 * 100
        ma_score = min(15, max(0, ma_diff_pct * 3))  # 差距5%得满分
        
        # 计算当前价格相对MA5的位置得分（15分）
        price_ma5_diff = (data['close'].iloc[-1] - ma5) / ma5 * 100
        if 0 <= price_ma5_diff <= 3:  # 价格在MA5上方0-3%范围内得满分
            price_position_score = 15
        elif 0 <= price_ma5_diff <= 5:  # 价格在MA5上方3-5%范围内得10分
            price_position_score = 10
        elif 0 <= price_ma5_diff <= 8:  # 价格在MA5上方5-8%范围内得5分
            price_position_score = 5
        else:
            price_position_score = 0  # 价格在MA5下方或偏离太远得0分
            
        return ma_score + price_position_score

    def calculate_volume_scores(self, data: pd.DataFrame) -> pd.Series:
        """计算成交量得分（30分）"""
        # 计算5日和10日平均成交量的比值
        volume_ratio = data['volume_ma5'] / data['volume_ma10']
        
        # 成交量趋势得分（15分）
        volume_trend_score = volume_ratio.apply(lambda x: (
            15 if x >= 1.5 else      # 5日量能明显放大
            10 if 1.2 <= x < 1.5 else  # 5日量能适度放大
            5 if 1.0 <= x < 1.2 else   # 5日量能略有放大
            2 if 0.8 <= x < 1.0 else   # 5日量能略有萎缩
            0                          # 5日量能明显萎缩
        ))
        
        # 成交额排名得分（15分）
        amount_score = 15 * (1 - data['amount_rank'] / data['total_stocks'])
        
        return volume_trend_score + amount_score

    def calculate_volatility_scores(self, data: pd.DataFrame) -> pd.Series:
        """计算波动得分（20分）"""
        # 振幅得分（10分）
        amplitude_score = (15 - data['amplitude_5d']) * 2/3
        amplitude_score = amplitude_score.clip(0, 10)
        
        # 日内波动率得分（10分）
        volatility_score = (10 - data['daily_volatility']).clip(0, 10)
        
        return amplitude_score + volatility_score

    def calculate_price_score(self, data: pd.DataFrame) -> float:
        """计算价格得分（20分）
        
        Args:
            data: 股票数据
            
        Returns:
            float: 价格得分
        """
        latest_price = data['close'].iloc[-1]
        
        # 计算价格合理性得分（10分）
        if 10 <= latest_price <= 50:  # 理想价格区间
            price_range_score = 10
        elif 5 <= latest_price <= 80:  # 较好价格区间
            price_range_score = 7
        elif 2 <= latest_price <= 100:  # 可接受价格区间
            price_range_score = 4
        else:
            price_range_score = 0
            
        # 计算股价位置得分（10分）
        price_range = data['close'].max() - data['close'].min()
        if price_range == 0:
            position_score = 5
        else:
            position_ratio = (latest_price - data['close'].min()) / price_range
            if 0.4 <= position_ratio <= 0.6:  # 价格在中间位置
                position_score = 10
            elif 0.2 <= position_ratio <= 0.8:  # 价格在较好位置
                position_score = 7
            else:  # 价格在极端位置
                position_score = 3
                
        return price_range_score + position_score
        
    def pick_stocks(self, date: str, market: Optional[str] = None) -> pd.DataFrame:
        """实现选股逻辑
        
        Args:
            date: 选股日期
            market: 可选，市场类型 'sh' 或 'sz'
            
        Returns:
            DataFrame: 选中的股票列表
        """
        # 构建SQL查询语句，直接从stock_history表中获取数据
        sql = """
        WITH StockData AS (
            SELECT 
                h.*,
                RANK() OVER (ORDER BY h.amount DESC) as amount_rank,
                COUNT(*) OVER () as total_stocks,
                LAG(h.close, 1) OVER (PARTITION BY h.stock_code ORDER BY h.date) as prev_close,
                LAG(h.close, 2) OVER (PARTITION BY h.stock_code ORDER BY h.date) as prev_close2,
                LAG(h.close, 3) OVER (PARTITION BY h.stock_code ORDER BY h.date) as prev_close3,
                (h.high - h.close) / h.close as high_fall_ratio,  -- 冲高回落比例
                (h.close - h.low) / h.low as low_rise_ratio      -- 探底回升比例
            FROM stock_history h
            WHERE h.date <= ?
                AND h.date >= date(?, '-60 days')
        )
        SELECT *
        FROM StockData
        WHERE date = ?
            AND amount >= 300000000  -- 成交额大于3亿
            AND amplitude_5d < 15    -- 过去5日内振幅<15%
            AND close BETWEEN 5 AND 200  -- 价格在5-200之间
            AND volume_ratio_5d BETWEEN 0.5 AND 2  -- 成交量在5日均量的0.5-2倍之间
            AND ma5 > ma10  -- MA5 > MA10（短期上升趋势）
            AND close > ma5  -- 当前价格 > MA5（股价在均线上方）
            AND daily_volatility < 5  -- 日内波动率不超过5%
            AND stock_code NOT LIKE '%ST%'  -- 排除ST股票
            AND NOT (  -- 排除最近3日连续下跌
                prev_close < close 
                AND prev_close2 < prev_close 
                AND prev_close3 < prev_close2
            )
            AND high_fall_ratio < 0.04  -- 限制冲高回落幅度小于4%
            AND low_rise_ratio > 0.01   -- 要求探底回升幅度大于1%
        """
        
        # 获取数据库连接
        conn = self.db.get_connection()
        
        # 根据市场类型修改SQL
        if market:
            if market == 'sh':
                sql = sql.replace('WHERE date = ?', "WHERE stock_code LIKE '6%' AND date = ?")
            elif market == 'sz':
                sql = sql.replace('WHERE date = ?', "WHERE (stock_code LIKE '000%' OR stock_code LIKE '002%' OR stock_code LIKE '300%') AND date = ?")
            
        # 执行查询
        selected = pd.read_sql(sql, conn, params=(date, date, date))
        
        if selected.empty:
            return pd.DataFrame()
            
        # 计算各维度得分
        selected['trend_score'] = self.calculate_trend_scores(selected)
        selected['volume_score'] = self.calculate_volume_scores(selected)
        selected['volatility_score'] = self.calculate_volatility_scores(selected)
        selected['pressure_support_score'] = self.calculate_pressure_support_scores(selected)
        selected['momentum_score'] = self.calculate_momentum_scores(selected)
        
        # 计算总分
        selected['total_score'] = (
            selected['trend_score'] + 
            selected['volume_score'] + 
            selected['volatility_score'] + 
            selected['pressure_support_score'] +
            selected['momentum_score']
        )
        
        # 计算建议买入价
        selected['suggested_buy_price'] = self.calculate_suggested_buy_price(selected)
        
        # 按总分降序排序
        return selected.sort_values('total_score', ascending=False)

    def calculate_trend_scores(self, data: pd.DataFrame) -> pd.Series:
        """计算趋势得分（30分）"""
        # MA5与MA10的差距得分（15分）
        ma_diff_pct = (data['ma5'] - data['ma10']) / data['ma10'] * 100
        ma_score = ma_diff_pct.apply(lambda x: (
            15 if 2 <= x <= 5 else  # 趋势加强但未过热
            10 if 1 <= x < 2 else   # 趋势开始形成
            5 if 5 < x <= 8 else    # 趋势略有过热
            2 if x > 8 else         # 趋势过热
            3 if 0.5 <= x < 1 else  # 趋势较弱
            0                       # 趋势太弱
        ))
        
        # 价格相对MA5的位置得分（15分）
        price_ma5_diff = data['ma5_deviation']
        price_position_score = price_ma5_diff.apply(lambda x: (
            15 if 1 <= x <= 3 else     # 价格在MA5上方1-3%，趋势强劲
            10 if 0.5 <= x < 1 else    # 价格刚刚突破MA5
            8 if 3 < x <= 5 else       # 价格偏离MA5较多，有回调风险
            3 if 0 <= x < 0.5 else     # 价格贴近MA5，支撑不稳
            2 if 5 < x <= 8 else       # 价格偏离MA5过多，回调风险大
            0                          # 价格在MA5下方或偏离过大
        ))
        
        return ma_score + price_position_score

    def calculate_pressure_support_scores(self, data: pd.DataFrame) -> pd.Series:
        """计算压力支撑得分（30分）"""
        # 压力指标得分（15分）
        pressure_score = data.apply(lambda x: (
            15 if x['volume_price_pressure_5d'] < 30 and x['pressure_ma5'] < 0.3 else
            10 if x['volume_price_pressure_5d'] < 50 and x['pressure_ma5'] < 0.5 else
            5 if x['volume_price_pressure_5d'] < 70 and x['pressure_ma5'] < 0.7 else
            0
        ), axis=1)
        
        # 支撑指标得分（15分）
        support_score = data.apply(lambda x: (
            15 if x['volume_price_support_5d'] > 70 and x['support_ma5'] > 0.7 else
            10 if x['volume_price_support_5d'] > 50 and x['support_ma5'] > 0.5 else
            5 if x['volume_price_support_5d'] > 30 and x['support_ma5'] > 0.3 else
            0
        ), axis=1)
        
        return pressure_score + support_score

    def calculate_momentum_scores(self, data: pd.DataFrame) -> pd.Series:
        """计算动量得分"""
        # 突破得分
        breakthrough_score = data.apply(lambda x: (
            10 if pd.notnull(x['breakthrough_ma5']) and pd.notnull(x['breakthrough_ma10']) and 
                x['breakthrough_ma5'] > 0.7 and x['breakthrough_ma10'] > 0.5 else
            5 if pd.notnull(x['breakthrough_ma5']) and pd.notnull(x['breakthrough_ma10']) and 
                x['breakthrough_ma5'] > 0.5 and x['breakthrough_ma10'] > 0.3 else
            0
        ), axis=1)
        
        # 回调得分
        pullback_score = data.apply(lambda x: (
            10 if pd.notnull(x['pullback_ma5']) and pd.notnull(x['pullback_ma10']) and 
                x['pullback_ma5'] > 0.7 and x['pullback_ma10'] > 0.5 else
            5 if pd.notnull(x['pullback_ma5']) and pd.notnull(x['pullback_ma10']) and 
                x['pullback_ma5'] > 0.5 and x['pullback_ma10'] > 0.3 else
            0
        ), axis=1)
        
        # 波动得分
        volatility_score = data.apply(lambda x: (
            10 if pd.notnull(x['daily_volatility']) and x['daily_volatility'] > 0.03 else
            5 if pd.notnull(x['daily_volatility']) and x['daily_volatility'] > 0.02 else
            0
        ), axis=1)
        
        # 振幅得分
        amplitude_score = data.apply(lambda x: (
            10 if pd.notnull(x['amplitude_5d']) and x['amplitude_5d'] > 0.1 else
            5 if pd.notnull(x['amplitude_5d']) and x['amplitude_5d'] > 0.05 else
            0
        ), axis=1)
        
        # 综合得分
        total_score = breakthrough_score + pullback_score + volatility_score + amplitude_score
        
        return total_score

    def calculate_suggested_buy_price(self, data: pd.DataFrame) -> pd.Series:
        """计算建议买入价格"""
        return data.apply(lambda x: (
            # 基础价格计算
            x['close'] * (
                # 压力支撑因子
                (1 - 0.01 * (x['volume_price_pressure_5d'] / 100)) *
                # 动量因子
                (1 + 0.005 * (1 if x['breakthrough_ma5'] > 0.5 else -1)) *
                # 趋势因子
                (1 + 0.003 * (x['ma5_deviation'] if abs(x['ma5_deviation']) < 5 else 0))
            )
        ), axis=1).round(2)

    def run(self, date: str, market: Optional[str] = None, print_info: bool = True) -> pd.DataFrame:
        # 尝试从缓存获取数据
        cached_data = self._get_cached_data(date, market)
        if cached_data is not None:
            return cached_data
            
        # 如果没有缓存，执行选股逻辑
        result = self.pick_stocks(date, market)
        
        # 缓存结果
        self._cache_data(date, market, result)
        
        return result

if __name__ == '__main__':
    try:
        # 创建策略实例
        strategy = SelectStrategy1()
        
        # 获取最新交易日期
        latest_date = strategy.db.get_latest_trading_date()
        print(f"\n正在运行选股策略：选股策略一")
        print(f"选股日期：{latest_date}")
        print("\n开始选股...")
        
        # 运行选股策略 - 包含沪深两市
        selected_sh = strategy.run(date=latest_date, market='sh')
        selected_sz = strategy.run(date=latest_date, market='sz')
        
        # 合并结果并去除多余的列
        selected = pd.concat([selected_sh, selected_sz])
        if not selected.empty:
            selected = selected[['stock_code', 'stock_name', 'close', 'amount', 'amplitude', 'total_score']]
        
        # 打印选股结果
        if not selected.empty:
            # 按总分排序
            selected = selected.sort_values('total_score', ascending=False)
            # 重置索引从1开始
            selected.index = range(1, len(selected) + 1)
            
            print(f"\n选股结果：共选出 {len(selected)} 只股票")
            print("\n序号  股票代码    名称    现价    成交额    振幅     评分")
            print("-" * 70)
            
            # 格式化输出每一行
            for idx, row in selected.iterrows():
                # 统一股票代码格式
                stock_code = row['stock_code']
                if not stock_code.startswith(('sh.', 'sz.')):
                    if stock_code.startswith('6'):
                        stock_code = 'sh.' + stock_code
                    else:
                        stock_code = 'sz.' + stock_code
                        
                amount_str = f"{row['amount']/1e8:.2f}亿"
                print(f"{idx:2d}  {stock_code:<9s} {row['stock_name']:<8s} {row['close']:6.2f}  {amount_str:>7s}  {row['amplitude']:7.2f}%  {row['total_score']:6.2f}")
        else:
            print("\n未找到符合条件的股票")
    except Exception as e:
        print(f"运行过程中发生错误: {str(e)}") 