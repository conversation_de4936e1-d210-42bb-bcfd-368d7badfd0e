"""
调试买入条件
"""

from .strategies.position_strategy import PositionStrategy
from .strategies.market_sentiment_strategy import MarketSentimentStrategy

def debug_buy_conditions(date: str):
    strategy = PositionStrategy()
    market_strategy = MarketSentimentStrategy()
    
    print(f"\n=== 调试买入条件 ({date}) ===")
    
    # 1. 检查市场情绪
    market_sentiment = market_strategy.get_market_sentiment(date)
    print(f"\n1. 市场情绪: {market_sentiment}")
    
    # 2. 获取候选股票
    top_stocks = strategy.get_top_stocks(date)
    print(f"\n2. 候选股票数量: {len(top_stocks) if not top_stocks.empty else 0}")
    
    if not top_stocks.empty:
        print("\n候选股票列表:")
        for _, stock in top_stocks.iterrows():
            print(f"股票代码: {stock['stock_code']}")
            print(f"收盘价: {stock['close']:.2f}")
            print(f"总得分: {stock['total_score']:.2f}")
            
            # 检查买入条件
            should_buy, reason = strategy.check_buy_conditions(stock['stock_code'], date)
            print(f"是否满足买入条件: {should_buy}")
            print(f"原因: {reason}")
            
            # 如果满足买入条件，计算可买数量
            if should_buy:
                buy_amount = 1000000 * strategy.INITIAL_POSITION_RATIO  # 假设总资产100万
                shares = int(buy_amount / stock['close'] / 100) * 100
                print(f"可买入股数: {shares}")
            print("-" * 50)

if __name__ == '__main__':
    # 设置PYTHONPATH
    import os
    import sys
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    sys.path.insert(0, project_root)
    debug_buy_conditions('2025-01-22')  # 使用当前测试的日期 