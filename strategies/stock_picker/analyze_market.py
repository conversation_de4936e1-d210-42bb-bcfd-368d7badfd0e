"""
分析市场情绪分布
"""

import os
import sys
import pandas as pd
import sqlite3

def analyze_market_sentiment():
    """分析市场情绪分布"""
    # 连接数据库
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                          'stock_data.db')
    conn = sqlite3.connect(db_path)
    
    # 使用窗口函数计算每个交易日的涨跌比
    query = """
    WITH daily_changes AS (
        SELECT 
            h1.date,
            h1.stock_code,
            h1.close as today_close,
            LAG(h1.close) OVER (PARTITION BY h1.stock_code ORDER BY h1.date) as prev_close
        FROM stock_history h1
        WHERE h1.close IS NOT NULL
    ),
    daily_stats AS (
        SELECT 
            date,
            COUNT(CASE WHEN (today_close - prev_close) > 0 THEN 1 END) as rising_count,
            COUNT(CASE WHEN (today_close - prev_close) < 0 THEN 1 END) as falling_count,
            COUNT(*) as total_stocks
        FROM daily_changes
        WHERE prev_close IS NOT NULL AND prev_close > 0
        GROUP BY date
    )
    SELECT 
        date,
        rising_count,
        falling_count,
        total_stocks,
        CAST(rising_count AS FLOAT) / NULLIF(falling_count, 0) as rise_fall_ratio,
        CASE 
            WHEN CAST(rising_count AS FLOAT) / NULLIF(falling_count, 0) < 0.3 THEN 1 
            ELSE 0 
        END as is_bear_market
    FROM daily_stats
    ORDER BY date
    """
    
    df = pd.read_sql(query, conn)
    
    # 计算熊市占比
    total_days = len(df)
    bear_days = df['is_bear_market'].sum()
    bear_ratio = bear_days / total_days * 100 if total_days > 0 else 0
    
    print(f"\n市场情绪统计:")
    print(f"总交易日数: {total_days}")
    print(f"涨跌比<0.3的天数: {bear_days}")
    print(f"熊市占比: {bear_ratio:.2f}%")
    
    # 计算涨跌比的分布
    print("\n涨跌比分布:")
    bins = [0, 0.3, 0.5, 1.0, 1.5, float('inf')]
    labels = ['<0.3', '0.3-0.5', '0.5-1.0', '1.0-1.5', '>1.5']
    df['ratio_range'] = pd.cut(df['rise_fall_ratio'], bins=bins, labels=labels)
    distribution = df['ratio_range'].value_counts().sort_index()
    for range_label, count in distribution.items():
        ratio = count / total_days * 100
        print(f"{range_label}: {count} 天 ({ratio:.2f}%)")
    
    # 输出极端情况的日期
    print("\n涨跌比<0.3的日期:")
    bear_days = df[df['rise_fall_ratio'] < 0.3].sort_values('rise_fall_ratio')
    for _, row in bear_days.head().iterrows():
        print(f"日期: {row['date']}, 涨跌比: {row['rise_fall_ratio']:.2f}, "
              f"上涨/下跌: {row['rising_count']}/{row['falling_count']}")
              
    print("\n涨跌比>1.5的日期:")
    bull_days = df[df['rise_fall_ratio'] > 1.5].sort_values('rise_fall_ratio', ascending=False)
    for _, row in bull_days.head().iterrows():
        print(f"日期: {row['date']}, 涨跌比: {row['rise_fall_ratio']:.2f}, "
              f"上涨/下跌: {row['rising_count']}/{row['falling_count']}")
    
    return df

if __name__ == '__main__':
    analyze_market_sentiment() 