"""
调试选股数据的来源
"""

import sys
import os
import pandas as pd
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from strategies.stock_picker.strategies.position_strategy import PositionStrategy
from strategies.utils.db_utils import DBUtils

def debug_selection_source(date: str):
    """调试选股数据的来源"""
    print(f"\n=== 测试日期: {date} ===")
    
    # 初始化策略
    strategy = PositionStrategy()
    
    # 记录开始时间
    start_time = time.time()
    
    # 获取选股结果
    results = strategy.get_top_stocks(date)
    
    # 计算耗时
    elapsed_time = time.time() - start_time
    
    print(f"\n获取选股结果耗时: {elapsed_time:.2f} 秒")
    
    if not results.empty:
        print("\n选股结果:")
        for _, row in results.iterrows():
            print(f"\n股票代码: {row['stock_code']}")
            print(f"收盘价: {row['close']:.2f}")
            print(f"总得分: {row['total_score']:.2f}")
            print("-" * 50)
    
    # 检查临时表中是否有数据
    db = DBUtils()
    conn = db.get_connection()
    query = "SELECT COUNT(*) as count FROM stock_selection_results WHERE date = ?"
    count = pd.read_sql(query, conn, params=(date,))['count'].iloc[0]
    print(f"\n临时表中该日期的数据条数: {count}")

if __name__ == '__main__':
    test_date = '2025-01-16'
    debug_selection_source(test_date) 