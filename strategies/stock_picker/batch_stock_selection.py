"""
批量计算选股结果并存储到临时表中
执行方式：
cd /Users/<USER>/wubin/backtrader/data
PYTHONPATH=/Users/<USER>/wubin/backtrader/data python3 -m strategies.stock_picker.batch_stock_selection
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta
from tqdm import tqdm

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(os.path.dirname(current_dir))
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

from strategies.utils.db_utils import DBUtils
from strategies.stock_picker.strategies.select_strategy_1 import SelectStrategy1

def create_temp_table():
    """创建临时表来存储选股结果"""
    db = DBUtils()
    conn = db.get_connection()
    
    # 创建临时表（如果不存在）
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS stock_selection_results (
        date DATE,
        stock_code VARCHAR(10),
        market VARCHAR(5),
        close FLOAT,
        total_score FLOAT,
        rank INT,
        PRIMARY KEY (date, stock_code)
    )
    """
    
    conn.execute(create_table_sql)
    conn.commit()

def get_date_range(start_date: str, end_date: str) -> list:
    """获取指定日期范围内的所有交易日"""
    db = DBUtils()
    conn = db.get_connection()
    
    query = """
    SELECT DISTINCT date
    FROM stock_history
    WHERE date BETWEEN ? AND ?
    ORDER BY date
    """
    
    dates = pd.read_sql(query, conn, params=(start_date, end_date))
    return dates['date'].tolist()

def batch_select_stocks(start_date: str, end_date: str):
    """批量计算选股结果并存储"""
    # 创建临时表
    create_temp_table()
    
    # 获取日期范围
    trading_dates = get_date_range(start_date, end_date)
    print(f"开始处理{len(trading_dates)}个交易日的数据...")
    
    # 初始化选股策略
    stock_picker = SelectStrategy1()
    db = DBUtils()
    conn = db.get_connection()
    
    # 清空指定日期范围内的数据
    clear_sql = "DELETE FROM stock_selection_results WHERE date BETWEEN ? AND ?"
    conn.execute(clear_sql, (start_date, end_date))
    conn.commit()
    
    # 批量处理每个交易日
    for date in tqdm(trading_dates, desc="处理进度"):
        # 获取沪深两市的选股结果
        selected_sh = stock_picker.run(date=date, market='sh', print_info=False)
        selected_sz = stock_picker.run(date=date, market='sz', print_info=False)
        
        # 合并结果
        selected = pd.concat([selected_sh, selected_sz])
        if not selected.empty:
            # 按得分排序
            selected = selected.sort_values('total_score', ascending=False)
            selected['rank'] = range(1, len(selected) + 1)
            
            # 准备插入数据
            for _, row in selected.iterrows():
                insert_sql = """
                INSERT INTO stock_selection_results 
                (date, stock_code, market, close, total_score, rank)
                VALUES (?, ?, ?, ?, ?, ?)
                """
                
                market = 'sh' if row['stock_code'].startswith('6') else 'sz'
                conn.execute(insert_sql, (
                    date,
                    row['stock_code'],
                    market,
                    row['close'],
                    row['total_score'],
                    row['rank']
                ))
            
            conn.commit()

def get_stored_selection_results(date: str, top_n: int = None) -> pd.DataFrame:
    """从临时表中获取指定日期的选股结果"""
    db = DBUtils()
    conn = db.get_connection()
    
    query = """
    SELECT *
    FROM stock_selection_results
    WHERE date = ?
    ORDER BY rank
    """
    
    if top_n is not None:
        query += f" LIMIT {top_n}"
    
    results = pd.read_sql(query, conn, params=(date,))
    return results

if __name__ == '__main__':
    start_date = '2025-05-28'  # 修改为数据库中的最早日期
    end_date = '2025-06-03'    # 修改为数据库中的最晚日期
    
    print(f"开始批量处理选股结果（{start_date} 至 {end_date}）...")
    batch_select_stocks(start_date, end_date)
    print("处理完成！") 