"""
测试特定日期的选股结果
"""

import sys
import os
import pandas as pd

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from strategies.stock_picker.batch_stock_selection import get_stored_selection_results

def test_selection(date: str, top_n: int = 10):
    """测试特定日期的选股结果"""
    print(f"\n=== {date} 选股结果（前{top_n}名）===")
    
    # 获取选股结果
    results = get_stored_selection_results(date, top_n)
    
    if results.empty:
        print(f"未找到 {date} 的选股结果")
        return
        
    # 打印结果
    for _, row in results.iterrows():
        print(f"\n股票代码: {row['stock_code']}")
        print(f"市场: {row['market']}")
        print(f"收盘价: {row['close']:.2f}")
        print(f"总得分: {row['total_score']:.2f}")
        print(f"排名: {row['rank']}")
        print("-" * 50)

if __name__ == '__main__':
    test_date = '2025-01-16'
    test_selection(test_date) 