"""
检查数据库中的日期范围
"""

import sys
import os
import pandas as pd

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from strategies.utils.db_utils import DBUtils

def check_date_range():
    """检查数据库中的日期范围"""
    db = DBUtils()
    conn = db.get_connection()
    
    # 获取最早的日期
    query_min = "SELECT MIN(date) as min_date FROM stock_history"
    min_date = pd.read_sql(query_min, conn)['min_date'].iloc[0]
    
    # 获取最晚的日期
    query_max = "SELECT MAX(date) as max_date FROM stock_history"
    max_date = pd.read_sql(query_max, conn)['max_date'].iloc[0]
    
    print(f"数据库中的日期范围：")
    print(f"最早日期：{min_date}")
    print(f"最晚日期：{max_date}")
    
    # 获取2024年的交易日数量
    query_2024 = """
    SELECT COUNT(DISTINCT date) as date_count
    FROM stock_history
    WHERE date BETWEEN '2024-01-01' AND '2024-12-31'
    """
    count_2024 = pd.read_sql(query_2024, conn)['date_count'].iloc[0]
    print(f"\n2024年的交易日数量：{count_2024}")
    
    # 获取2024年1月16日到5月30日的交易日数量
    query_range = """
    SELECT COUNT(DISTINCT date) as date_count
    FROM stock_history
    WHERE date BETWEEN '2024-01-16' AND '2024-05-30'
    """
    count_range = pd.read_sql(query_range, conn)['date_count'].iloc[0]
    print(f"2024年1月16日到5月30日的交易日数量：{count_range}")

if __name__ == '__main__':
    check_date_range() 