"""
数据库工具类
"""

import os
import sqlite3
import pandas as pd
from typing import Optional, List, Union
from datetime import datetime, timedelta

class DBUtils:
    _instance = None
    
    def __new__(cls, db_path: Optional[str] = None):
        if cls._instance is None:
            cls._instance = super(DBUtils, cls).__new__(cls)
            # 修改默认数据库路径
            cls._instance.db_path = db_path or os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'stock_data.db')
            cls._instance.conn = None
            cls._instance.cursor = None
            cls._instance.connect()
        elif db_path is not None and db_path != cls._instance.db_path:
            cls._instance.db_path = db_path
            if cls._instance.conn:
                cls._instance.conn.close()
                cls._instance.conn = None
                cls._instance.cursor = None
        return cls._instance

    def connect(self):
        """连接数据库"""
        if not self.conn:
            self.conn = sqlite3.connect(self.db_path)
            self.cursor = self.conn.cursor()

    def ensure_indexes(self):
        """确保必要的索引存在"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # 为stock_history表创建索引
        cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_stock_history_date 
        ON stock_history(date)
        """)
        
        cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_stock_history_code_date 
        ON stock_history(stock_code, date)
        """)
        
        cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_stock_history_date_code 
        ON stock_history(date, stock_code)
        """)
        
        conn.commit()

    def get_connection(self):
        """获取数据库连接"""
        if self.conn is None:
            self.conn = sqlite3.connect(self.db_path)
        return self.conn

    def close(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
            self.conn = None
            self.cursor = None

    def get_stock_list(self, market: Optional[str] = None) -> pd.DataFrame:
        """获取股票列表
        
        Args:
            market: 可选，市场类型 'sh' 或 'sz'
            
        Returns:
            DataFrame: 包含股票信息的DataFrame
        """
        conn = self.get_connection()
        
        # 从stock_history表获取最新的股票列表
        latest_date = self.get_latest_trading_date()
        
        # 构建查询
        query = """
        SELECT DISTINCT 
            stock_code,
            stock_name
        FROM stock_history
        WHERE date = ?
        """
        
        # 根据市场类型过滤
        if market:
            if market == 'sh':
                query += " AND stock_code LIKE '6%'"
            elif market == 'sz':
                query += " AND (stock_code LIKE '000%' OR stock_code LIKE '002%' OR stock_code LIKE '300%')"
        
        stocks = pd.read_sql(query, conn, params=(latest_date,))
        return stocks

    def get_daily_data(self, stock_code: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> pd.DataFrame:
        """获取股票的日线数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD
        
        Returns:
            DataFrame: 股票日线数据
        """
        conn = self.get_connection()
        
        # 如果没有指定开始日期，获取end_date之前的60个交易日的数据
        if not start_date and end_date:
            query = f"""
            SELECT DISTINCT date 
            FROM stock_history 
            WHERE date <= '{end_date}'
            ORDER BY date DESC 
            LIMIT 60
            """
            dates = pd.read_sql(query, conn)
            if not dates.empty:
                start_date = dates.iloc[-1]['date']
        
        # 构建SQL查询
        sql = """
        SELECT 
            stock_code,
            stock_name,
            date,
            open,
            high,
            low,
            close,
            volume,
            amount,
            amplitude,
            change_percent,
            change_amount,
            turnover_rate,
            net_profit,
            total_value,
            float_value,
            industry,
            pe_ratio,
            pb_ratio,
            roe,
            gross_profit_margin,
            net_profit_margin,
            sector_code,
            ma5,
            ma10,
            ma20,
            ma30,
            ma60,
            volume_ma5,
            volume_ma10,
            volume_ma20,
            amplitude_5d,
            amplitude_10d,
            daily_volatility,
            volume_ratio_5d,
            volume_ratio_10d,
            ma5_deviation,
            ma10_deviation,
            ma_trend,
            pressure_ma5,
            pressure_ma10,
            pressure_ma20,
            support_ma5,
            support_ma10,
            support_ma20,
            volume_price_pressure_5d,
            volume_price_pressure_10d,
            volume_price_support_5d,
            volume_price_support_10d,
            volume_distribution_high,
            volume_distribution_mid,
            volume_distribution_low,
            breakthrough_ma5,
            breakthrough_ma10,
            pullback_ma5,
            pullback_ma10
        FROM stock_history
        WHERE stock_code = ?
        """
        
        if start_date:
            query += f" AND date >= '{start_date}'"
        if end_date:
            query += f" AND date <= '{end_date}'"
        query += " ORDER BY date"
        
        df = pd.read_sql(sql, conn, params=(stock_code,))
        
        if not df.empty:
            # 设置日期为索引
            df.set_index('date', inplace=True)
            
            # 计算技术指标
            df['ma5'] = df['close'].rolling(window=5).mean()
            df['ma10'] = df['close'].rolling(window=10).mean()
            df['ma20'] = df['close'].rolling(window=20).mean()
            df['volume_ma5'] = df['volume'].rolling(window=5).mean()
            df['volume_ma10'] = df['volume'].rolling(window=10).mean()
            
        return df

    def get_latest_trading_date(self) -> str:
        """获取数据库中最新的交易日期"""
        conn = self.get_connection()
        query = "SELECT MAX(date) as latest_date FROM stock_history"
        result = pd.read_sql(query, conn)
        return result['latest_date'].iloc[0]

    def get_stock_data_by_date(self, date: str, market: Optional[str] = None) -> pd.DataFrame:
        """获取指定日期的所有股票数据
        
        Args:
            date: 日期，格式：YYYY-MM-DD
            market: 可选，市场类型 'sh' 或 'sz'
        
        Returns:
            包含当日所有股票数据的DataFrame
        """
        conn = self.get_connection()
        
        # 构建查询，包含所有字段
        query = """
        SELECT *
        FROM stock_history
        WHERE date = ?
        """
        
        # 根据市场类型过滤
        if market:
            if market == 'sh':
                query += " AND stock_code LIKE '6%'"
            elif market == 'sz':
                query += " AND (stock_code LIKE '000%' OR stock_code LIKE '002%' OR stock_code LIKE '300%')"
        
        df = pd.read_sql(query, conn, params=(date,))
        
        if not df.empty:
            # 计算技术指标
            stock_codes = df['stock_code'].unique()
            ma_data = []
            
            for code in stock_codes:
                hist_data = self.get_daily_data(code, end_date=date)
                if not hist_data.empty:
                    ma_data.append({
                        'stock_code': code,
                        'ma5': hist_data['ma5'].iloc[-1] if len(hist_data) >= 5 else None,
                        'ma10': hist_data['ma10'].iloc[-1] if len(hist_data) >= 10 else None,
                        'ma20': hist_data['ma20'].iloc[-1] if len(hist_data) >= 20 else None,
                        'volume_ma5': hist_data['volume_ma5'].iloc[-1] if len(hist_data) >= 5 else None,
                        'volume_ma10': hist_data['volume_ma10'].iloc[-1] if len(hist_data) >= 10 else None
                    })
            
            if ma_data:
                ma_df = pd.DataFrame(ma_data)
                df = df.merge(ma_df, on='stock_code', how='left')
            
        return df

    def get_trading_days_between(self, start_date: str, end_date: str) -> List[str]:
        """获取两个日期之间的所有交易日
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            交易日列表
        """
        conn = self.get_connection()
        query = f"""
        SELECT DISTINCT date 
        FROM stock_history 
        WHERE date >= '{start_date}' AND date <= '{end_date}'
        ORDER BY date
        """
        dates = pd.read_sql(query, conn)
        return dates['date'].tolist()

    def analyze_market_sentiment_distribution(self) -> pd.DataFrame:
        """分析市场情绪分布
        
        Returns:
            DataFrame包含每个交易日的涨跌比统计
        """
        conn = self.get_connection()
        
        # 使用窗口函数计算每个交易日的涨跌比
        query = """
        WITH daily_changes AS (
            SELECT 
                h1.date,
                h1.stock_code,
                h1.close as today_close,
                LAG(h1.close) OVER (PARTITION BY h1.stock_code ORDER BY h1.date) as prev_close
            FROM stock_history h1
            WHERE h1.close IS NOT NULL
        ),
        daily_stats AS (
            SELECT 
                date,
                COUNT(CASE WHEN (today_close - prev_close) > 0 THEN 1 END) as rising_count,
                COUNT(CASE WHEN (today_close - prev_close) < 0 THEN 1 END) as falling_count,
                COUNT(*) as total_stocks
            FROM daily_changes
            WHERE prev_close IS NOT NULL AND prev_close > 0
            GROUP BY date
        )
        SELECT 
            date,
            rising_count,
            falling_count,
            total_stocks,
            CAST(rising_count AS FLOAT) / NULLIF(falling_count, 0) as rise_fall_ratio,
            CASE 
                WHEN CAST(rising_count AS FLOAT) / NULLIF(falling_count, 0) < 0.3 THEN 1 
                ELSE 0 
            END as is_bear_market
        FROM daily_stats
        ORDER BY date
        """
        
        df = pd.read_sql(query, conn)
        
        # 计算熊市占比
        total_days = len(df)
        bear_days = df['is_bear_market'].sum()
        bear_ratio = bear_days / total_days * 100 if total_days > 0 else 0
        
        print(f"\n市场情绪统计:")
        print(f"总交易日数: {total_days}")
        print(f"涨跌比<0.3的天数: {bear_days}")
        print(f"熊市占比: {bear_ratio:.2f}%")
        
        # 计算涨跌比的分布
        print("\n涨跌比分布:")
        bins = [0, 0.3, 0.5, 1.0, 1.5, float('inf')]
        labels = ['<0.3', '0.3-0.5', '0.5-1.0', '1.0-1.5', '>1.5']
        df['ratio_range'] = pd.cut(df['rise_fall_ratio'], bins=bins, labels=labels)
        distribution = df['ratio_range'].value_counts().sort_index()
        for range_label, count in distribution.items():
            ratio = count / total_days * 100
            print(f"{range_label}: {count} 天 ({ratio:.2f}%)")
        
        return df

    def get_stock_history(self, stock_code: str, end_date: str, days: int = 30) -> pd.DataFrame:
        """
        获取股票历史数据
        
        Args:
            stock_code: 股票代码
            end_date: 结束日期，格式：YYYY-MM-DD
            days: 获取的天数
            
        Returns:
            DataFrame包含以下列：
            - date: 日期
            - open: 开盘价
            - high: 最高价
            - low: 最低价
            - close: 收盘价
            - volume: 成交量
            - amount: 成交额
            - ma5, ma10, ma20: 移动平均线
            - volume_ma5, volume_ma10: 成交量移动平均
        """
        try:
            # 确保连接是打开的
            self.connect()
            
            # 计算起始日期
            end_date = pd.to_datetime(end_date)
            start_date = end_date - pd.Timedelta(days=days)
            
            # 构建SQL查询
            query = """
            SELECT date, open, high, low, close, volume, amount,
                   ma5, ma10, ma20, volume_ma5, volume_ma10,
                   change_percent, turnover_rate
            FROM stock_history
            WHERE stock_code = ? AND date BETWEEN ? AND ?
            ORDER BY date
            """
            
            # 执行查询
            df = pd.read_sql_query(
                query, 
                self.conn,
                params=(stock_code, start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d'))
            )
            
            # 转换日期列
            df['date'] = pd.to_datetime(df['date'])
            
            return df
            
        except Exception as e:
            print(f"获取股票 {stock_code} 历史数据失败: {str(e)}")
            return pd.DataFrame()

    def __del__(self):
        self.close()

# 使用示例
if __name__ == '__main__':
    # 创建数据库工具实例
    db = DBUtils()
    
    # 获取所有上证股票列表
    sh_stocks = db.get_stock_list()
    print(f"上证股票数量：{len(sh_stocks)}")
    
    # 获取某只股票的日线数据
    if len(sh_stocks) > 0:
        stock_code = sh_stocks['stock_code'].iloc[0]
        daily_data = db.get_daily_data(stock_code, '2023-01-01', '2023-12-31')
        print(f"股票 {stock_code} 的数据量：{len(daily_data)}")
    
    # 获取最新交易日期
    latest_date = db.get_latest_trading_date()
    print(f"最新交易日期：{latest_date}")
    
    # 获取最新交易日的所有股票数据
    if latest_date:
        latest_data = db.get_stock_data_by_date(latest_date)
        print(f"最新交易日股票数量：{len(latest_data)}")
