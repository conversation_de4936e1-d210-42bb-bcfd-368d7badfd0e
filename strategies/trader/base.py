from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple
import pandas as pd
from ..utils.db_utils import DBUtils

class BaseTrader(ABC):
    """交易策略基类"""
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.db = DBUtils()
        self.positions: Dict[str, float] = {}  # 持仓信息：{股票代码: 持仓比例}
        self.stop_loss: float = 0.1  # 默认止损比例
        self.stop_profit: float = 0.2  # 默认止盈比例
        self.max_position_per_stock: float = 0.2  # 单只股票最大持仓比例
        
    @abstractmethod
    def generate_signals(self, stock_pool: pd.DataFrame, date: str) -> pd.DataFrame:
        """生成交易信号，需要在子类中实现
        
        Args:
            stock_pool: 股票池DataFrame
            date: 交易日期
            
        Returns:
            DataFrame: 包含交易信号的DataFrame，至少包含以下列：
                - code: 股票代码
                - signal: 1（买入）、0（持有）、-1（卖出）
                - weight: 建议仓位比例
        """
        pass
    
    def get_history_data(self, stock_code: str, days: int = 30) -> pd.DataFrame:
        """获取历史数据
        
        Args:
            stock_code: 股票代码
            days: 历史数据天数
            
        Returns:
            DataFrame: 历史数据
        """
        latest_date = self.db.get_latest_trading_date()
        data = self.db.get_daily_data(stock_code)
        return data.tail(days)
    
    def check_stop_conditions(self, positions: Dict[str, float], 
                            current_prices: pd.DataFrame) -> List[str]:
        """检查止损止盈条件
        
        Args:
            positions: 当前持仓信息
            current_prices: 当前价格数据
            
        Returns:
            List[str]: 需要平仓的股票代码列表
        """
        stocks_to_sell = []
        for code in positions:
            if code not in current_prices.index:
                continue
                
            current_price = current_prices.loc[code, 'close']
            avg_cost = positions[code]  # 这里假设positions中存储了成本价
            
            # 检查止损条件
            if current_price < avg_cost * (1 - self.stop_loss):
                stocks_to_sell.append(code)
                continue
                
            # 检查止盈条件
            if current_price > avg_cost * (1 + self.stop_profit):
                stocks_to_sell.append(code)
                
        return stocks_to_sell
    
    def adjust_position_sizes(self, signals: pd.DataFrame) -> pd.DataFrame:
        """调整持仓比例
        
        Args:
            signals: 交易信号DataFrame
            
        Returns:
            DataFrame: 调整后的交易信号
        """
        # 确保不超过单只股票最大持仓比例
        signals.loc[signals['weight'] > self.max_position_per_stock, 'weight'] = self.max_position_per_stock
        
        # 如果总仓位超过100%，等比例缩减
        total_weight = signals[signals['signal'] == 1]['weight'].sum()
        if total_weight > 1:
            signals.loc[signals['signal'] == 1, 'weight'] *= 1 / total_weight
            
        return signals
    
    def run(self, stock_pool: pd.DataFrame, date: Optional[str] = None) -> pd.DataFrame:
        """运行交易策略
        
        Args:
            stock_pool: 股票池DataFrame
            date: 可选，交易日期，默认为最新交易日
            
        Returns:
            DataFrame: 交易信号
        """
        if date is None:
            date = self.db.get_latest_trading_date()
            
        print(f"正在运行交易策略：{self.name}")
        print(f"交易日期：{date}")
        print(f"股票池大小：{len(stock_pool)}")
        
        # 生成交易信号
        signals = self.generate_signals(stock_pool, date)
        
        # 调整持仓比例
        signals = self.adjust_position_sizes(signals)
        
        # 检查止损止盈
        if len(self.positions) > 0:
            current_prices = stock_pool.set_index('code')
            stocks_to_sell = self.check_stop_conditions(self.positions, current_prices)
            signals.loc[signals['code'].isin(stocks_to_sell), 'signal'] = -1
            
        return signals
        
    def get_positions(self) -> Dict[str, float]:
        """获取当前持仓信息"""
        return self.positions.copy()
        
    def update_positions(self, trades: pd.DataFrame):
        """更新持仓信息
        
        Args:
            trades: 交易记录DataFrame，包含 code 和 weight 列
        """
        for _, trade in trades.iterrows():
            if trade['signal'] == 1:  # 买入
                self.positions[trade['code']] = trade['weight']
            elif trade['signal'] == -1:  # 卖出
                if trade['code'] in self.positions:
                    del self.positions[trade['code']]
