import sqlite3
import os
from datetime import datetime
from pathlib import Path

def merge_databases(remote_db_path, local_db_path):
    """
    将远程数据库的数据合并到本地数据库
    """
    try:
        # 连接远程数据库（实际是下载到本地的远程数据库文件）
        remote_conn = sqlite3.connect(remote_db_path)
        remote_cursor = remote_conn.cursor()
        
        # 连接或创建本地数据库
        local_conn = sqlite3.connect(local_db_path)
        local_cursor = local_conn.cursor()
        
        # 在本地创建表（如果不存在）
        local_cursor.execute('''
        CREATE TABLE IF NOT EXISTS stock_history (
            stock_code TEXT,
            date TEXT,
            open REAL,
            high REAL,
            low REAL,
            close REAL,
            volume REAL,
            amount REAL,
            PRIMARY KEY (stock_code, date)
        )
        ''')
        local_conn.commit()
        
        # 获取远程数据库的数据
        remote_cursor.execute('SELECT * FROM stock_history')
        batch_size = 1000
        count = 0
        
        while True:
            rows = remote_cursor.fetchmany(batch_size)
            if not rows:
                break
                
            # 使用 INSERT OR REPLACE 来处理可能的冲突
            local_cursor.executemany('''
            INSERT OR REPLACE INTO stock_history (
                stock_code, date, open, high, low, close, volume, amount
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', rows)
            
            count += len(rows)
            print(f"\r已处理 {count} 条记录", end="", flush=True)
            
            local_conn.commit()
        
        print("\n数据合并完成！")
        
        # 显示统计信息
        local_cursor.execute('SELECT COUNT(*) FROM stock_history')
        total_records = local_cursor.fetchone()[0]
        
        local_cursor.execute('SELECT COUNT(DISTINCT stock_code) FROM stock_history')
        total_stocks = local_cursor.fetchone()[0]
        
        local_cursor.execute('SELECT MIN(date), MAX(date) FROM stock_history')
        min_date, max_date = local_cursor.fetchone()
        
        print(f"\n统计信息：")
        print(f"总记录数：{total_records}")
        print(f"股票数量：{total_stocks}")
        print(f"数据范围：{min_date} 至 {max_date}")
        
    finally:
        remote_conn.close()
        local_conn.close()

if __name__ == "__main__":
    # 指定数据库文件路径
    scripts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'test_will_delete', 'scripts')
    remote_db = os.path.join(scripts_dir, "stock_data_remote.db")  # 从远程下载到本地的数据库文件
    local_db = os.path.join(scripts_dir, "stock_data.db")  # 本地数据库文件
    
    # 开始合并
    print(f"开始合并数据库...")
    print(f"远程数据库文件：{remote_db}")
    print(f"本地数据库文件：{local_db}")
    
    merge_databases(remote_db, local_db) 