import efinance as ef
import pandas as pd
import sqlite3
from pathlib import Path
import logging
import time
import random
from datetime import datetime, timedelta
import argparse
from tqdm import tqdm
from functools import wraps
import os

def setup_logging():
    """配置日志"""
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建一个日志文件，文件名包含时间戳
    log_file = os.path.join(log_dir, f"download_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def retry_with_delay(max_retries=3, base_delay=2):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    if attempt > 0:
                        delay = base_delay * (attempt + 1) + random.uniform(0, 1)
                        logging.info(f"第 {attempt + 1} 次尝试, 等待 {delay:.1f} 秒...")
                        time.sleep(delay)
                    return func(*args, **kwargs)
                except Exception as e:
                    logging.error(f"第 {attempt + 1} 次尝试失败: {str(e)}")
                    if attempt == max_retries - 1:
                        raise
            return None
        return wrapper
    return decorator

@retry_with_delay()
def get_stock_data(stock_code, start_date, end_date):
    """获取股票数据"""
    try:
        # 获取日K线数据
        df = ef.stock.get_quote_history(stock_code, beg=start_date, end=end_date)
        
        if df is not None and not df.empty:
            # 重命名列
            df = df.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '涨跌幅': 'change_percent',
                '涨跌额': 'change_amount',
                '换手率': 'turnover_rate'
            })
            
            # 获取股票基本信息
            stock_info = ef.stock.get_base_info(stock_code)
            if not stock_info.empty:
                # 添加基本信息字段
                for key in stock_info.index:
                    field_name = key.lower().replace(' ', '_')
                    df[field_name] = stock_info[key]
            
            return df
        
        return None
    except Exception as e:
        logging.error(f"获取数据异常 - 股票: {stock_code}, 错误: {str(e)}")
        raise

def save_to_database(df, stock_code, conn):
    """将数据保存到数据库"""
    try:
        if df is not None and not df.empty:
            # 准备要保存的数据
            df_save = df.copy()
            
            # 确保所有数值列都是数值类型
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount',
                             'amplitude', 'change_percent', 'change_amount', 'turnover_rate']
            for col in numeric_columns:
                if col in df_save.columns:
                    df_save[col] = pd.to_numeric(df_save[col], errors='coerce')
            
            # 字段映射
            field_mapping = {
                '股票名称': 'stock_name',
                '净利润': 'net_profit',
                '总市值': 'total_value',
                '流通市值': 'float_value',
                '所处行业': 'industry',
                '市盈率(动)': 'pe_ratio',
                '市净率': 'pb_ratio',
                'roe': 'roe',
                '毛利率': 'gross_profit_margin',
                '净利率': 'net_profit_margin',
                '板块编号': 'sector_code'
            }
            
            # 重命名列
            for old_name, new_name in field_mapping.items():
                if old_name in df_save.columns:
                    df_save[new_name] = df_save[old_name]
            
            # 转换为记录列表
            records = df_save.to_dict('records')
            
            # 准备SQL语句，只使用实际存在的字段
            available_columns = ['stock_code', 'stock_name', 'date', 'open', 'high', 'low', 'close', 
                               'volume', 'amount', 'amplitude', 'change_percent', 'change_amount', 
                               'turnover_rate', 'net_profit', 'total_value', 'float_value', 'industry',
                               'pe_ratio', 'pb_ratio', 'roe', 'gross_profit_margin', 'net_profit_margin',
                               'sector_code']
            
            # 只包含存在的列
            actual_columns = [col for col in available_columns if col in df_save.columns]
            
            # 构建SQL语句
            columns = ', '.join(actual_columns)
            placeholders = ', '.join([':' + col for col in actual_columns])
            
            cursor = conn.cursor()
            sql = f'''
            INSERT OR REPLACE INTO stock_history (
                {columns}
            ) VALUES (
                {placeholders}
            )
            '''
            
            cursor.executemany(sql, records)
            conn.commit()
            return True
    except Exception as e:
        logging.error(f"保存数据异常 - 股票: {stock_code}, 错误: {str(e)}")
        return False

def get_stock_progress(conn, stock_code):
    """获取股票的下载进度"""
    try:
        cursor = conn.cursor()
        cursor.execute('''
        SELECT MAX(date) as last_date
        FROM stock_history
        WHERE stock_code = ?
        ''', (stock_code,))
        result = cursor.fetchone()
        return result[0] if result and result[0] else None
    except Exception as e:
        logging.error(f"获取进度异常 - 股票: {stock_code}, 错误: {str(e)}")
        return None

def get_stock_list_from_csv(market_type=None):
    """从CSV文件获取股票列表"""
    try:
        # 读取CSV文件
        csv_path = "test_will_delete/scripts/stock_list_full.csv"
        df = pd.read_csv(csv_path)
        
        # 将股票代码转为字符串并补零到6位
        df['股票代码'] = df['股票代码'].astype(str).str.zfill(6)
        
        # 添加市场类型列（根据股票代码判断）
        df['market'] = df['股票代码'].apply(lambda x: 'sh' if str(x).startswith(('6', '688')) else 'sz')
        
        # 根据市场类型筛选
        if market_type:
            df = df[df['market'] == market_type]
        
        # 返回股票代码和名称列表
        return list(zip(df['股票代码'], df['股票名称']))
    except Exception as e:
        logging.error(f"从CSV获取股票列表异常: {str(e)}")
        return []

def process_single_stock(stock_code, stock_name, start_date, end_date, conn):
    """处理单只股票的数据下载"""
    logging.info(f"开始处理股票: {stock_code} ({stock_name})")
    
    try:
        # 获取断点续传的位置
        last_date = get_stock_progress(conn, stock_code)
        if last_date:
            # 如果有历史数据，从最后一天的下一天开始下载
            start = datetime.strptime(last_date, '%Y-%m-%d') + timedelta(days=1)
            if start > datetime.strptime(end_date, '%Y%m%d'):
                logging.info(f"股票 {stock_code} 数据已是最新")
                return True
            start_date = start.strftime('%Y%m%d')
            logging.info(f"股票 {stock_code} 将从 {start_date} 继续下载")
        
        # 获取数据
        df = get_stock_data(stock_code, start_date, end_date)
        
        # 添加随机延迟，避免请求过于频繁
        time.sleep(random.uniform(0.2, 0.5))
        
        if df is not None and not df.empty:
            # 添加股票代码列
            df['stock_code'] = stock_code
            
            # 保存到数据库
            if save_to_database(df, stock_code, conn):
                logging.info(f"股票 {stock_code} ({stock_name}) 数据保存成功")
                return True
            else:
                logging.error(f"股票 {stock_code} ({stock_name}) 数据保存失败")
                return False
        else:
            logging.warning(f"股票 {stock_code} ({stock_name}) 没有数据")
            return True  # 返回True因为这种情况不需要重试
        
    except Exception as e:
        logging.error(f"处理异常 - 股票: {stock_code}, 错误: {str(e)}")
        return False

def create_tables(conn):
    """创建必要的数据表"""
    cursor = conn.cursor()
    
    # 创建股票历史数据表，包含所有字段
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS stock_history (
        stock_code TEXT,
        stock_name TEXT,
        date TEXT,
        open REAL,
        high REAL,
        low REAL,
        close REAL,
        volume REAL,
        amount REAL,
        amplitude REAL,
        change_percent REAL,
        change_amount REAL,
        turnover_rate REAL,
        net_profit REAL,          -- 净利润
        total_value REAL,         -- 总市值
        float_value REAL,         -- 流通市值
        industry TEXT,            -- 所处行业
        pe_ratio REAL,           -- 市盈率(动)
        pb_ratio REAL,           -- 市净率
        roe REAL,                -- ROE
        gross_profit_margin REAL, -- 毛利率
        net_profit_margin REAL,   -- 净利率
        sector_code TEXT,         -- 板块编号
        PRIMARY KEY (stock_code, date)
    )
    ''')
    
    conn.commit()

def main():
    parser = argparse.ArgumentParser(description='下载股票历史数据')
    parser.add_argument('--market', type=str, choices=['sh', 'sz'], help='市场类型：sh-上海，sz-深圳')
    parser.add_argument('--start_date', type=str, help='开始日期，格式：YYYYMMDD')
    parser.add_argument('--end_date', type=str, help='结束日期，格式：YYYYMMDD')
    args = parser.parse_args()

    setup_logging()
    
    # 创建数据库连接
    db_path = Path('stock_data.db')
    conn = sqlite3.connect(db_path)
    
    try:
        # 创建表
        create_tables(conn)
        
        # 从CSV文件获取股票列表
        stock_list = get_stock_list_from_csv(args.market)
        
        if not stock_list:
            logging.error(f"没有找到{args.market}市场的股票")
            return
        
        logging.info(f"找到 {len(stock_list)} 只股票")
        
        # 使用tqdm创建进度条
        with tqdm(total=len(stock_list), desc="下载进度") as pbar:
            for stock_code, stock_name in stock_list:
                success = process_single_stock(stock_code, stock_name, args.start_date, args.end_date, conn)
                if not success:
                    logging.warning(f"股票 {stock_code} 处理失败")
                pbar.update(1)
                
    except Exception as e:
        logging.error(f"程序执行异常: {str(e)}")
    finally:
        conn.close()

if __name__ == '__main__':
    main() 