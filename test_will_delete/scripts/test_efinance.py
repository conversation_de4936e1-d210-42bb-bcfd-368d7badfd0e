import efinance as ef
import pandas as pd
import time
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
from requests import Session

# 配置重试机制
def get_session():
    session = Session()
    retries = Retry(total=5,
                   backoff_factor=1,
                   status_forcelist=[500, 502, 503, 504])
    session.mount('http://', HTTPAdapter(max_retries=retries))
    session.mount('https://', HTTPAdapter(max_retries=retries))
    return session

# 测试用的股票代码列表
test_stocks = ['600519', '000001', '601318', '600036', '000858']  # 茅台、平安银行、中国平安、招商银行、五粮液

print("\n获取示例股票的日线数据...")
for stock in test_stocks:
    try:
        # 每次请求之间添加延时
        time.sleep(2)
        
        # 获取数据
        df = ef.stock.get_quote_history(stock, klt=101, beg='20240101', end='20240331')
        print(f"\n股票代码 {stock} 的数据示例:")
        print("数据字段:", df.columns.tolist())
        print("数据前5行:")
        print(df.head())
    except Exception as e:
        print(f"获取股票 {stock} 数据时出错: {str(e)}")
        # 如果出错，多等待一会再继续
        time.sleep(5) 