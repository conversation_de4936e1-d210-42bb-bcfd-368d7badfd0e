import sqlite3
from datetime import datetime
import pandas as pd
from pathlib import Path

def check_data_completeness():
    """检查股票数据完整性"""
    # 连接数据库
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    
    try:
        # 获取所有股票列表，确保stock_code是字符串类型
        stocks_df = pd.read_sql(
            "SELECT CAST(stock_code AS TEXT) as stock_code, stock_name FROM stocks ORDER BY stock_code", 
            conn
        )
        
        # 获取历史数据的统计信息，确保stock_code是字符串类型
        history_stats = pd.read_sql("""
            SELECT 
                CAST(stock_code AS TEXT) as stock_code,
                MIN(date) as start_date,
                MAX(date) as end_date,
                COUNT(DISTINCT date) as trading_days
            FROM stock_history h
            GROUP BY h.stock_code
        """, conn)
        
        # 合并股票信息和统计数据
        result = pd.merge(stocks_df, history_stats, on='stock_code', how='left')
        
        # 填充没有数据的股票
        result['trading_days'] = result['trading_days'].fillna(0).astype(int)
        
        # 计算统计信息
        total_stocks = len(result)
        stocks_with_data = len(result[result['trading_days'] > 0])
        stocks_no_data = len(result[result['trading_days'] == 0])
        
        print("\n=== 数据完整性检查报告 ===")
        print(f"\n1. 基本统计:")
        print(f"   - 总股票数: {total_stocks}")
        print(f"   - 有数据的股票数: {stocks_with_data}")
        print(f"   - 无数据的股票数: {stocks_no_data}")
        
        if stocks_with_data > 0:
            # 获取有数据的股票的日期范围
            stocks_with_data_df = result[result['trading_days'] > 0]
            if not stocks_with_data_df.empty:
                print("\n2. 数据日期范围:")
                print(f"   - 最早数据日期: {stocks_with_data_df['start_date'].min()}")
                print(f"   - 最新数据日期: {stocks_with_data_df['end_date'].max()}")
            
            # 计算交易日统计
            trading_days_stats = stocks_with_data_df['trading_days'].describe()
            print("\n3. 交易日数量统计:")
            print(f"   - 平均交易日数: {trading_days_stats['mean']:.2f}")
            print(f"   - 最少交易日数: {trading_days_stats['min']:.0f}")
            print(f"   - 最多交易日数: {trading_days_stats['max']:.0f}")
            
            # 显示数据不完整的股票
            max_days = trading_days_stats['max']
            incomplete_stocks = stocks_with_data_df[
                stocks_with_data_df['trading_days'] < max_days
            ].sort_values('trading_days')
            
            if len(incomplete_stocks) > 0:
                print("\n4. 数据不完整的股票 (前10个):")
                for _, row in incomplete_stocks.head(10).iterrows():
                    print(f"   - {row['stock_code']} ({row['stock_name']}): "
                          f"{row['trading_days']}天 ({row['start_date']} 至 {row['end_date']})")
            
            # 显示没有数据的股票
            if stocks_no_data > 0:
                print("\n5. 没有数据的股票 (前10个):")
                no_data_stocks = result[result['trading_days'] == 0]
                for _, row in no_data_stocks.head(10).iterrows():
                    print(f"   - {row['stock_code']} ({row['stock_name']})")
        
    finally:
        conn.close()

if __name__ == "__main__":
    check_data_completeness() 