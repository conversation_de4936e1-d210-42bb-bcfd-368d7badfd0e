import sqlite3
from pathlib import Path

def check_progress():
    """检查下载进度"""
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    
    try:
        cursor = conn.cursor()
        
        # 1. 检查数据库中的表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print("\n数据库中的表:")
        for table in tables:
            print(f"- {table[0]}")
        
        # 2. 检查stock_history表中的数据
        cursor.execute("""
        SELECT 
            stock_code,
            MIN(date) as first_date,
            MAX(date) as last_date,
            COUNT(*) as record_count
        FROM stock_history
        GROUP BY stock_code
        ORDER BY stock_code DESC
        LIMIT 5
        """)
        
        print("\n最近下载的5只股票:")
        rows = cursor.fetchall()
        for row in rows:
            print(f"股票代码: {row[0]}")
            print(f"  首条数据: {row[1]}")
            print(f"  最后数据: {row[2]}")
            print(f"  数据条数: {row[3]}")
            print()
        
        # 3. 统计总体情况
        cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM stock_history")
        total_stocks = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM stock_history")
        total_records = cursor.fetchone()[0]
        
        print("\n总体统计:")
        print(f"已下载股票数: {total_stocks}")
        print(f"总数据条数: {total_records}")
        
    finally:
        conn.close()

if __name__ == "__main__":
    check_progress() 