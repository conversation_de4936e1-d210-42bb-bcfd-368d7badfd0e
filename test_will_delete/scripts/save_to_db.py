import pandas as pd
import sqlite3
from pathlib import Path
import os

def create_stock_table(conn):
    """
    创建股票信息表
    """
    cursor = conn.cursor()
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS stocks (
        stock_code TEXT PRIMARY KEY,
        stock_name TEXT,
        market_type TEXT,
        latest_price REAL,
        high_price REAL,
        low_price REAL,
        open_price REAL,
        prev_close REAL,
        change_percent REAL,
        change_amount REAL,
        turnover_rate REAL,
        volume_ratio REAL,
        pe_ratio REAL,
        volume REAL,
        amount REAL,
        total_market_value REAL,
        circulating_market_value REAL,
        quote_id TEXT,
        update_time TEXT,
        latest_trading_day TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    conn.commit()

def convert_numeric(value):
    """
    转换数值类型，处理特殊字符
    """
    if isinstance(value, str):
        # 移除可能的百分号和其他特殊字符
        value = value.replace('%', '').replace(',', '')
        try:
            return float(value)
        except (ValueError, TypeError):
            return None
    return value

def save_stocks_to_db(csv_file, db_path):
    """
    将股票数据保存到数据库
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file)
        
        # 重命名列为英文
        column_mapping = {
            '股票代码': 'stock_code',
            '股票名称': 'stock_name',
            '市场类型': 'market_type',
            '最新价': 'latest_price',
            '最高': 'high_price',
            '最低': 'low_price',
            '今开': 'open_price',
            '昨日收盘': 'prev_close',
            '涨跌幅': 'change_percent',
            '涨跌额': 'change_amount',
            '换手率': 'turnover_rate',
            '量比': 'volume_ratio',
            '动态市盈率': 'pe_ratio',
            '成交量': 'volume',
            '成交额': 'amount',
            '总市值': 'total_market_value',
            '流通市值': 'circulating_market_value',
            '行情ID': 'quote_id',
            '更新时间': 'update_time',
            '最新交易日': 'latest_trading_day'
        }
        df = df.rename(columns=column_mapping)
        
        # 转换数值类型的列
        numeric_columns = ['latest_price', 'high_price', 'low_price', 'open_price', 
                         'prev_close', 'change_percent', 'change_amount', 'turnover_rate',
                         'volume_ratio', 'pe_ratio', 'volume', 'amount', 
                         'total_market_value', 'circulating_market_value']
        
        for col in numeric_columns:
            if col in df.columns:
                df[col] = df[col].apply(convert_numeric)
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        
        # 创建表
        create_stock_table(conn)
        
        # 将数据写入数据库
        df.to_sql('stocks', conn, if_exists='replace', index=False)
        
        # 获取记录数
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM stocks")
        count = cursor.fetchone()[0]
        
        print(f"成功将 {count} 条股票数据保存到数据库")
        print(f"数据库路径: {db_path}")
        
        # 关闭连接
        conn.close()
        
    except Exception as e:
        print(f"保存数据到数据库时出错: {str(e)}")

if __name__ == "__main__":
    # 设置文件路径
    current_dir = Path(os.path.dirname(os.path.abspath(__file__)))
    csv_file = current_dir / "stock_list_full.csv"
    db_path = current_dir / "stock_data.db"
    
    if not csv_file.exists():
        print(f"错误: CSV文件不存在: {csv_file}")
    else:
        save_stocks_to_db(str(csv_file), str(db_path)) 