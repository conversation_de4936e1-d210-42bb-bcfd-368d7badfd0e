import baostock as bs
import pandas as pd
import sqlite3
from pathlib import Path
import logging
import time
import random
from datetime import datetime, timedelta

def setup_logging():
    """配置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def get_daily_data(stock_code, date, bs_session):
    """获取单日数据"""
    try:
        # 确保股票代码是6位格式
        stock_code = str(stock_code).zfill(6)
        
        # 添加市场标识
        bs_code = f"sh.{stock_code}" if stock_code.startswith('6') else f"sz.{stock_code}"
        
        # 获取日K线数据
        rs = bs_session.query_history_k_data_plus(
            bs_code,
            "date,code,open,high,low,close,volume,amount",
            start_date=date,
            end_date=date,
            frequency="d",
            adjustflag="3"
        )
        
        if rs.error_code != '0':
            logging.error(f"获取数据失败 - 股票: {stock_code}, 日期: {date}, 错误: {rs.error_msg}")
            return None
        
        df = rs.get_data()
        if df is not None and not df.empty:
            # 重命名列
            df.columns = ['date', 'stock_code', 'open', 'high', 'low', 'close', 'volume', 'amount']
            
            # 转换数据类型
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 确保股票代码是6位格式
            df['stock_code'] = df['stock_code'].apply(lambda x: str(x).zfill(6))
            
            return df
        
        return None
    except Exception as e:
        logging.error(f"处理异常 - 股票: {stock_code}, 日期: {date}, 错误: {str(e)}")
        return None

def save_to_database(df, stock_code, conn):
    """将数据保存到数据库"""
    try:
        if df is not None and not df.empty:
            df_save = df.copy()
            # 确保股票代码是6位格式
            df_save['stock_code'] = df_save['stock_code'].apply(lambda x: str(x).zfill(6))
            
            records = df_save.to_dict('records')
            
            cursor = conn.cursor()
            cursor.executemany('''
            INSERT OR REPLACE INTO stock_history (
                stock_code, date, open, high, low, close, volume, amount
            ) VALUES (
                :stock_code, :date, :open, :high, :low, :close, :volume, :amount
            )
            ''', records)
            
            conn.commit()
            return True
    except Exception as e:
        logging.error(f"保存数据异常 - 股票: {stock_code}, 错误: {str(e)}")
        return False

def download_stock_data(stock_code, start_date, end_date):
    """下载指定股票的数据"""
    # 登录系统
    bs.login()
    
    try:
        # 连接数据库
        db_path = Path("stock_data.db")
        conn = sqlite3.connect(str(db_path))
        
        # 生成日期范围
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')
        date_range = []
        current = start
        while current <= end:
            date_range.append(current.strftime('%Y-%m-%d'))
            current += timedelta(days=1)
        
        success_count = 0
        fail_count = 0
        
        for date in date_range:
            try:
                print(f"\r当前处理日期: {date}", end="", flush=True)
                
                df = get_daily_data(stock_code, date, bs)
                
                if df is not None and not df.empty:
                    if save_to_database(df, stock_code, conn):
                        success_count += 1
                    else:
                        fail_count += 1
                else:
                    logging.debug(f"股票 {stock_code} 在 {date} 没有数据")
                
                # 请求间隔
                time.sleep(random.uniform(0.2, 0.3))
                
            except Exception as e:
                fail_count += 1
                logging.error(f"处理异常 - 股票: {stock_code}, 日期: {date}, 错误: {str(e)}")
                continue
        
        print(f"\n下载完成 - 成功: {success_count}, 失败: {fail_count}")
        
    finally:
        conn.close()
        bs.logout()

if __name__ == "__main__":
    setup_logging()
    # 下载000003的数据
    download_stock_data('000003', '2024-01-01', '2024-05-30') 