import efinance as ef
import pandas as pd
import sqlite3
from datetime import datetime, timedelta
import time
from pathlib import Path
from tqdm import tqdm
import os
from retry import retry
import random

def get_date_range(end_date_str='20240530', days=365):
    """
    生成日期范围
    """
    end_date = datetime.strptime(end_date_str, '%Y%m%d')
    date_list = []
    for i in range(days):
        current_date = end_date - timedelta(days=i)
        date_list.append(current_date.strftime('%Y%m%d'))
    return date_list

@retry(tries=5, delay=5, backoff=2, jitter=(1, 3))
def get_stock_list():
    """
    获取所有A股股票列表
    """
    try:
        # 随机延迟1-3秒
        time.sleep(random.uniform(1, 3))
        stock_df = ef.stock.get_realtime_quotes()
        return list(zip(stock_df['股票代码'], stock_df['股票名称']))
    except Exception as e:
        print(f"获取股票列表时出错: {str(e)}")
        raise

@retry(tries=5, delay=5, backoff=2, jitter=(1, 3))
def get_daily_data(stock_code, date):
    """
    获取指定股票在指定日期的数据
    """
    try:
        # 随机延迟1-3秒
        time.sleep(random.uniform(1, 3))
        
        # 获取当天的历史数据
        df = ef.stock.get_quote_history(stock_code, beg=date, end=date)
        if df is not None and not df.empty:
            # 重命名列
            df = df.rename(columns={
                '日期': 'trade_date',
                '开盘': 'open_price',
                '收盘': 'latest_price',
                '最高': 'high_price',
                '最低': 'low_price',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '涨跌幅': 'change_percent',
                '涨跌额': 'change_amount',
                '换手率': 'turnover_rate'
            })
            return df
    except Exception as e:
        print(f"\n获取股票 {stock_code} 在 {date} 的数据时出错: {str(e)}")
        raise
    return None

def save_to_database(df, stock_code, stock_name, market_type, conn):
    """
    将数据保存到数据库
    """
    try:
        # 创建daily_data表（如果不存在）
        cursor = conn.cursor()
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS daily_data (
            trade_date TEXT,
            stock_code TEXT,
            stock_name TEXT,
            latest_price REAL,
            open_price REAL,
            high_price REAL,
            low_price REAL,
            volume REAL,
            amount REAL,
            turnover_rate REAL,
            amplitude REAL,
            change_percent REAL,
            change_amount REAL,
            market_type TEXT,
            PRIMARY KEY (trade_date, stock_code)
        )
        ''')
        
        if df is not None and not df.empty:
            # 添加股票信息
            df['stock_code'] = stock_code
            df['stock_name'] = stock_name
            df['market_type'] = market_type
            
            # 将数据转换为列表
            records = df.to_dict('records')
            
            # 批量插入数据
            cursor.executemany('''
            INSERT OR REPLACE INTO daily_data (
                trade_date, stock_code, stock_name, latest_price, open_price,
                high_price, low_price, volume, amount,
                turnover_rate, amplitude, change_percent, change_amount,
                market_type
            ) VALUES (
                :trade_date, :stock_code, :stock_name, :latest_price, :open_price,
                :high_price, :low_price, :volume, :amount,
                :turnover_rate, :amplitude, :change_percent, :change_amount,
                :market_type
            )
            ''', records)
            
            conn.commit()
            return True
    except Exception as e:
        print(f"\n保存数据时出错: {str(e)}")
        return False

def main():
    # 连接数据库
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    
    try:
        # 获取股票列表
        print("获取股票列表...")
        for attempt in range(3):  # 最多尝试3次获取股票列表
            try:
                stock_list = get_stock_list()
                if stock_list:
                    break
            except Exception as e:
                print(f"第 {attempt + 1} 次尝试获取股票列表失败")
                if attempt < 2:  # 如果不是最后一次尝试，等待后重试
                    time.sleep(10)
                continue
        
        if not stock_list:
            print("无法获取股票列表，程序退出")
            return
        
        print(f"共获取到 {len(stock_list)} 只股票")
        
        # 获取日期范围（从5月30日开始，向前365天）
        dates = get_date_range()
        print(f"开始下载从 {dates[-1]} 到 {dates[0]} 的数据")
        
        total_success = 0
        total_fail = 0
        
        # 对每个日期
        for date in tqdm(dates, desc="日期进度"):
            day_success = 0
            day_fail = 0
            
            # 对每只股票
            for stock_code, stock_name in tqdm(stock_list, desc=f"处理 {date} 的股票", leave=False):
                try:
                    # 检查是否已存在数据
                    cursor = conn.cursor()
                    cursor.execute('''
                    SELECT 1 FROM daily_data 
                    WHERE trade_date = ? AND stock_code = ?
                    ''', (date, stock_code))
                    
                    if cursor.fetchone():
                        print(f"\n股票 {stock_code} 在 {date} 的数据已存在，跳过")
                        day_success += 1
                        continue
                    
                    # 获取数据
                    df = get_daily_data(stock_code, date)
                    
                    if df is not None and not df.empty:
                        # 确定市场类型
                        market_type = "沪市" if stock_code.startswith('6') else "深市"
                        
                        # 保存到数据库
                        if save_to_database(df, stock_code, stock_name, market_type, conn):
                            day_success += 1
                        else:
                            day_fail += 1
                    else:
                        day_fail += 1
                    
                    # 随机延迟1-3秒
                    time.sleep(random.uniform(1, 3))
                    
                except Exception as e:
                    day_fail += 1
                    continue
            
            total_success += day_success
            total_fail += day_fail
            print(f"\n{date} 处理完成: 成功 {day_success}, 失败 {day_fail}")
            
            # 每处理完一天，暂停5-10秒
            time.sleep(random.uniform(5, 10))
        
        print(f"\n全部下载完成! 总成功: {total_success}, 总失败: {total_fail}")
        
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
    finally:
        conn.close()

if __name__ == "__main__":
    main() 