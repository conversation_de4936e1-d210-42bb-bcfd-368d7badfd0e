import akshare as ak
import pandas as pd
import time
from datetime import datetime, timedelta

def format_date(date_str):
    """将日期字符串转换为YYYYMMDD格式"""
    try:
        date = datetime.strptime(date_str, "%Y%m%d")
        return date.strftime("%Y%m%d")
    except ValueError:
        return None

# 测试用的股票代码列表
test_stocks = ['600519', '000001', '601318', '600036', '000858']  # 茅台、平安银行、中国平安、招商银行、五粮液

# 设置较短的时间范围进行测试
end_date = datetime.now()
start_date = end_date - timedelta(days=30)  # 获取最近30天的数据
start_date_str = start_date.strftime("%Y%m%d")
end_date_str = end_date.strftime("%Y%m%d")

print(f"\n获取示例股票的日线数据 (从 {start_date_str} 到 {end_date_str})...")
for stock in test_stocks:
    try:
        print(f"\n尝试获取股票 {stock} 的数据...")
        
        # 每次请求之间添加延时
        time.sleep(3)
        
        # 获取数据
        df = ak.stock_zh_a_hist(
            symbol=stock,
            period="daily",
            start_date=start_date_str,
            end_date=end_date_str,
            adjust="qfq"  # 使用前复权数据
        )
        
        if df is not None and not df.empty:
            print(f"成功获取股票 {stock} 的数据")
            print("数据字段:", df.columns.tolist())
            print("数据前5行:")
            print(df.head())
        else:
            print(f"获取股票 {stock} 数据时返回空数据")
            
    except Exception as e:
        print(f"获取股票 {stock} 数据时出错: {str(e)}")
        # 如果出错，多等待一会再继续
        time.sleep(5)