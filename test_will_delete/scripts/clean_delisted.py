import sqlite3
from pathlib import Path
import logging

def setup_logging():
    """配置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def clean_delisted():
    """清理退市股票的数据"""
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    
    try:
        cursor = conn.cursor()
        
        # 要删除的退市股票列表
        delisted_stocks = ['000003']
        
        # 1. 从stocks表中删除
        for stock_code in delisted_stocks:
            cursor.execute("""
            DELETE FROM stocks 
            WHERE stock_code = ?
            """, (stock_code,))
            
            print(f"从stocks表中删除股票 {stock_code}")
        
        # 2. 从stock_history表中删除
        for stock_code in delisted_stocks:
            cursor.execute("""
            DELETE FROM stock_history 
            WHERE stock_code = ?
            """, (stock_code,))
            
            print(f"从stock_history表中删除股票 {stock_code}")
        
        # 3. 提交更改
        conn.commit()
        
        print("\n清理完成")
        
    finally:
        conn.close()

if __name__ == "__main__":
    setup_logging()
    clean_delisted() 