import sqlite3
from pathlib import Path

def fix_table_structure():
    """修复表结构和数据类型"""
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    
    try:
        cursor = conn.cursor()
        
        # 0. 先删除旧的stocks_new表（如果存在）
        cursor.execute("DROP TABLE IF EXISTS stocks_new")
        conn.commit()  # 确保删除操作被提交
        
        # 1. 创建新的stocks表（只保留必要字段）
        cursor.execute("""
        CREATE TABLE stocks_new (
            stock_code TEXT NOT NULL,
            stock_name TEXT NOT NULL
        )
        """)
        conn.commit()  # 确保创建操作被提交
        
        # 2. 复制数据到新表，同时修复stock_code格式
        cursor.execute("""
        INSERT INTO stocks_new
        SELECT DISTINCT
            CASE 
                WHEN length(cast(stock_code as text)) < 6 
                THEN substr('000000' || cast(stock_code as text), -6)
                ELSE cast(stock_code as text)
            END as stock_code,
            stock_name
        FROM stocks
        """)
        conn.commit()  # 确保插入操作被提交
        
        # 3. 删除旧表并重命名新表
        cursor.execute("DROP TABLE stocks")
        cursor.execute("ALTER TABLE stocks_new RENAME TO stocks")
        conn.commit()  # 确保删除和重命名操作被提交
        
        # 5. 验证修复结果
        print("\n=== 数据修复验证 ===")
        
        # 检查stocks表中的stock_code类型
        cursor.execute("SELECT DISTINCT typeof(stock_code) FROM stocks")
        print("\nstocks表中stock_code的类型:", cursor.fetchall())
        
        # 检查总记录数
        cursor.execute("SELECT COUNT(*) FROM stocks")
        total = cursor.fetchone()[0]
        print(f"stocks表总记录数: {total}")
        
        # 检查平安银行的数据
        cursor.execute("""
        SELECT stock_code, stock_name 
        FROM stocks 
        WHERE stock_code = '000001'
        """)
        stock = cursor.fetchone()
        if stock:
            print(f"\n平安银行数据:")
            print(f"股票代码: {stock[0]}")
            print(f"股票名称: {stock[1]}")
            
            # 检查历史数据
            cursor.execute("""
            SELECT COUNT(*) 
            FROM stock_history 
            WHERE stock_code = '000001'
            """)
            count = cursor.fetchone()[0]
            print(f"历史数据记录数: {count}")
            
            if count > 0:
                # 显示历史数据
                cursor.execute("""
                SELECT date, open, close, volume
                FROM stock_history
                WHERE stock_code = '000001'
                ORDER BY date
                """)
                print("\n历史数据记录:")
                for record in cursor.fetchall():
                    print(f"日期: {record[0]}, 开盘: {record[1]}, 收盘: {record[2]}, 成交量: {record[3]}")
        
        # 6. 检查一些示例数据
        print("\n其他示例数据:")
        cursor.execute("""
        SELECT stock_code, stock_name 
        FROM stocks 
        ORDER BY stock_code 
        LIMIT 5
        """)
        for record in cursor.fetchall():
            print(f"股票代码: {record[0]}, 名称: {record[1]}")
        
    finally:
        conn.close()

if __name__ == "__main__":
    fix_table_structure() 