import sqlite3
import pandas as pd
import efinance as ef
from datetime import datetime, timedelta
from pathlib import Path
import os
from tqdm import tqdm
import time
from retry import retry

def create_history_table(conn):
    """
    创建历史数据表
    """
    cursor = conn.cursor()
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS stock_history (
        stock_code TEXT,
        date TEXT,
        open REAL,
        high REAL,
        low REAL,
        close REAL,
        volume REAL,
        amount REAL,
        ma5 REAL,
        ma10 REAL,
        ma20 REAL,
        ma30 REAL,
        ma60 REAL,
        PRIMARY KEY (stock_code, date)
    )
    ''')
    conn.commit()

@retry(tries=3, delay=5, backoff=2)
def get_stock_history(stock_code, days=60):
    """
    获取股票历史数据并计算移动平均线
    """
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
    
    try:
        df = ef.stock.get_quote_history(stock_code, beg=start_date, end=end_date)
        if df is not None and not df.empty:
            # 重命名列
            df = df.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount'
            })
            
            # 确保日期格式正确
            df['date'] = pd.to_datetime(df['date']).dt.strftime('%Y-%m-%d')
            
            # 计算移动平均线
            for period in [5, 10, 20, 30, 60]:
                df[f'ma{period}'] = df['close'].rolling(window=period).mean()
            
            return df
        return None
    except Exception as e:
        print(f"获取股票 {stock_code} 历史数据时出错: {str(e)}")
        raise

def update_stock_history():
    """
    更新所有股票的历史数据和移动平均线
    """
    try:
        # 连接数据库
        db_path = Path("stock_data.db")
        conn = sqlite3.connect(str(db_path))
        
        # 创建历史数据表
        create_history_table(conn)
        
        # 从stocks表获取所有股票代码
        cursor = conn.cursor()
        cursor.execute("SELECT stock_code, stock_name FROM stocks")
        stocks = cursor.fetchall()
        
        if not stocks:
            print("数据库中没有股票信息")
            return
        
        print(f"开始更新 {len(stocks)} 只股票的历史数据...")
        
        success_count = 0
        fail_count = 0
        
        with tqdm(stocks, desc="更新进度") as pbar:
            for stock_code, stock_name in pbar:
                try:
                    # 获取历史数据
                    df = get_stock_history(stock_code)
                    
                    if df is not None and not df.empty:
                        # 将数据插入数据库
                        df['stock_code'] = stock_code
                        df.to_sql('stock_history', conn, if_exists='replace', index=False)
                        
                        print(f"\n成功更新股票 {stock_code} ({stock_name}) 的历史数据")
                        success_count += 1
                    else:
                        print(f"\n获取股票 {stock_code} ({stock_name}) 历史数据失败")
                        fail_count += 1
                    
                    # 更新进度条描述
                    pbar.set_description(f"更新进度 (成功: {success_count}, 失败: {fail_count})")
                    
                    # 添加延时避免请求过于频繁
                    time.sleep(2)
                    
                except Exception as e:
                    print(f"\n更新股票 {stock_code} ({stock_name}) 时出错: {str(e)}")
                    fail_count += 1
                    continue
        
        print(f"\n更新完成! 成功: {success_count}, 失败: {fail_count}")
        
    except Exception as e:
        print(f"更新过程中出错: {str(e)}")
    finally:
        conn.close()

if __name__ == "__main__":
    update_stock_history() 