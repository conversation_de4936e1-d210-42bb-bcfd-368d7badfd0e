import sqlite3
from pathlib import Path
import os

def query_examples():
    """
    展示一些查询示例
    """
    try:
        # 连接数据库
        current_dir = Path(os.path.dirname(os.path.abspath(__file__)))
        db_path = current_dir / "stock_data.db"
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 示例1: 查看市值前10的公司
        print("\n市值前10的公司:")
        print("=" * 50)
        cursor.execute("""
            SELECT stock_code, stock_name, total_market_value, market_type
            FROM stocks
            ORDER BY total_market_value DESC
            LIMIT 10
        """)
        for row in cursor.fetchall():
            print(f"代码: {row[0]}, 名称: {row[1]}, 总市值: {row[2]:.2f}, 市场: {row[3]}")
            
        # 示例2: 按市场类型统计股票数量
        print("\n各市场股票数量统计:")
        print("=" * 50)
        cursor.execute("""
            SELECT market_type, COUNT(*) as count
            FROM stocks
            GROUP BY market_type
        """)
        for row in cursor.fetchall():
            print(f"市场: {row[0]}, 数量: {row[1]}")
            
        # 示例3: 查看涨幅前10的股票
        print("\n涨幅前10的股票:")
        print("=" * 50)
        cursor.execute("""
            SELECT stock_code, stock_name, change_percent, latest_price
            FROM stocks
            ORDER BY change_percent DESC
            LIMIT 10
        """)
        for row in cursor.fetchall():
            print(f"代码: {row[0]}, 名称: {row[1]}, 涨幅: {row[2]:.2f}%, 最新价: {row[3]}")
        
        # 关闭连接
        conn.close()
        
    except Exception as e:
        print(f"查询数据时出错: {str(e)}")

if __name__ == "__main__":
    query_examples() 