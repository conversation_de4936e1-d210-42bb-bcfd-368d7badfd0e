import sqlite3
from pathlib import Path

def fix_stock_codes():
    """修复数据库中的股票代码格式"""
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    
    try:
        cursor = conn.cursor()
        
        # 1. 先获取需要修改的股票代码
        cursor.execute("""
        SELECT DISTINCT stock_code 
        FROM stocks 
        WHERE length(stock_code) < 6
        """)
        stocks_to_fix = cursor.fetchall()
        
        stocks_updated = 0
        history_updated = 0
        
        # 2. 对每个股票代码进行修复
        for (old_code,) in stocks_to_fix:
            new_code = str(old_code).zfill(6)
            
            # 修复stocks表
            cursor.execute("""
            UPDATE stocks 
            SET stock_code = ? 
            WHERE stock_code = ?
            """, (new_code, old_code))
            stocks_updated += cursor.rowcount
            
            # 修复stock_history表
            cursor.execute("""
            UPDATE stock_history 
            SET stock_code = ? 
            WHERE stock_code = ?
            """, (new_code, old_code))
            history_updated += cursor.rowcount
            
            # 每修复一个股票就提交一次，避免全部回滚
            conn.commit()
        
        print("\n=== 股票代码格式修复报告 ===")
        print(f"stocks表更新记录数: {stocks_updated}")
        print(f"stock_history表更新记录数: {history_updated}")
        
        # 验证修复结果
        cursor.execute("SELECT stock_code, stock_name FROM stocks WHERE stock_code = '000001'")
        stock = cursor.fetchone()
        if stock:
            print(f"\n验证平安银行股票代码:")
            print(f"股票代码: {stock[0]}")
            print(f"股票名称: {stock[1]}")
            
            cursor.execute("""
            SELECT COUNT(*) FROM stock_history 
            WHERE stock_code = '000001'
            """)
            count = cursor.fetchone()[0]
            print(f"历史数据记录数: {count}")
            
            # 显示具体数据
            cursor.execute("""
            SELECT date, open, close, volume 
            FROM stock_history 
            WHERE stock_code = '000001'
            ORDER BY date
            """)
            records = cursor.fetchall()
            if records:
                print("\n历史数据记录:")
                for record in records:
                    print(f"日期: {record[0]}, 开盘: {record[1]}, 收盘: {record[2]}, 成交量: {record[3]}")
        
    finally:
        conn.close()

if __name__ == "__main__":
    fix_stock_codes() 