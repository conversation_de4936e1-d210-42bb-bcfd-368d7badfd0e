import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
import time
from functools import wraps
import random

def retry_with_delay(max_retries=5, base_delay=10):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    if attempt > 0:  # 第一次尝试前不等待
                        delay = base_delay * (attempt + 1) + random.uniform(0, 3)
                        print(f"\n第 {attempt + 1} 次尝试, 等待 {delay:.1f} 秒...")
                        time.sleep(delay)
                    return func(*args, **kwargs)
                except Exception as e:
                    print(f"\n第 {attempt + 1} 次尝试失败: {str(e)}")
                    if attempt == max_retries - 1:  # 最后一次尝试
                        raise
            return None
        return wrapper
    return decorator

@retry_with_delay(max_retries=5, base_delay=10)
def get_stock_history():
    # 深圳股票代码需要加上.SZ后缀
    stock_code = "300999.SZ"
    
    print(f"\n尝试获取股票 {stock_code} 的历史数据...")
    
    # 创建Ticker对象
    stock = yf.Ticker(stock_code)
    
    # 打印股票基本信息
    info = stock.info
    print("\n股票基本信息:")
    print(f"股票名称: {info.get('longName', 'N/A')}")
    print(f"交易所: {info.get('exchange', 'N/A')}")
    print(f"市场: {info.get('market', 'N/A')}")
    print(f"时区: {info.get('timeZone', 'N/A')}")
    
    # 获取最近5个交易日的数据，包括5月28日
    end_date = datetime(2024, 5, 29)  # 5月29日
    start_date = end_date - timedelta(days=10)  # 获取多几天以确保包含5月28日
    
    print(f"\n获取日期范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    print("数据频率: 日线(1d)")
    
    # 获取历史数据
    hist = stock.history(
        start=start_date.strftime('%Y-%m-%d'),
        end=end_date.strftime('%Y-%m-%d'),
        interval='1d',  # 日线数据
        prepost=False,  # 不包含盘前盘后数据
        actions=True,   # 包含分红派息数据
        auto_adjust=True  # 自动复权
    )
    
    if hist.empty:
        print(f"未获取到股票 {stock_code} 的数据")
        return
        
    print(f"\n获取到 {len(hist)} 个交易日的数据")
    print("\n数据字段:", hist.columns.tolist())
    print("\n所有交易日期:")
    print(hist.index.tolist())
    
    # 尝试获取5月28日的数据
    target_date = '2024-05-28'
    if target_date in hist.index:
        row = hist.loc[target_date]
        print(f"\n{target_date} 的详细数据:")
        print(f"开盘价: {row['Open']:.2f}")
        print(f"最高价: {row['High']:.2f}")
        print(f"最低价: {row['Low']:.2f}")
        print(f"收盘价: {row['Close']:.2f}")
        print(f"成交量: {row['Volume']:,.0f}")
        print(f"股息: {row.get('Dividends', 'N/A')}")
        print(f"拆分: {row.get('Stock Splits', 'N/A')}")
    else:
        print(f"\n未找到 {target_date} 的数据")
        
    return hist

if __name__ == "__main__":
    try:
        get_stock_history()
    except Exception as e:
        print(f"\n最终获取数据失败: {str(e)}") 