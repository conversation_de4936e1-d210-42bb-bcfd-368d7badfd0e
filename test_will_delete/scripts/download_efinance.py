import efinance as ef
import pandas as pd
from datetime import datetime
import os
from tqdm import tqdm
import time
from retry import retry

@retry(tries=3, delay=5, backoff=2)
def get_stock_list():
    """
    获取所有A股股票列表
    """
    try:
        # 获取所有A股的股票代码和名称
        stock_list = ef.stock.get_realtime_quotes()
        return list(zip(stock_list['股票代码'], stock_list['股票名称']))
    except Exception as e:
        print(f"获取股票列表时出错: {str(e)}")
        raise  # 重新抛出异常以触发重试

@retry(tries=3, delay=5, backoff=2)
def download_stock_history(stock_code, stock_name, start_date, end_date):
    """
    下载单个股票的历史数据
    """
    try:
        # 获取日K线数据
        df = ef.stock.get_quote_history(stock_code, beg=start_date, end=end_date)
        
        if df is not None and not df.empty:
            # 重命名列
            df = df.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount'
            })
            
            # 确保日期格式正确
            df['date'] = pd.to_datetime(df['date'])
            
            return df
        
        return None
    
    except Exception as e:
        print(f"下载股票 {stock_code} ({stock_name}) 数据时出错: {str(e)}")
        raise  # 重新抛出异常以触发重试

def download_with_backup_source(stock_code, stock_name, start_date, end_date):
    """
    使用多个数据源尝试下载数据
    """
    try:
        # 首先尝试使用主数据源
        df = download_stock_history(stock_code, stock_name, start_date, end_date)
        if df is not None and not df.empty:
            return df
        
        # 如果主数据源失败，等待后重试
        print(f"主数据源获取失败，等待10秒后重试...")
        time.sleep(10)
        
        # 再次尝试
        df = download_stock_history(stock_code, stock_name, start_date, end_date)
        return df
        
    except Exception as e:
        print(f"所有数据源都失败: {str(e)}")
        return None

def main():
    # 设置日期范围
    start_date = '20240101'
    end_date = '20240530'
    
    # 创建保存数据的目录
    base_dir = "efinance_data"
    os.makedirs(base_dir, exist_ok=True)
    
    # 创建日志文件
    log_file = os.path.join(base_dir, "download_log.txt")
    failed_stocks_file = os.path.join(base_dir, "failed_stocks.txt")
    
    # 获取股票列表
    print("获取股票列表...")
    try:
        stock_list = get_stock_list()
    except Exception as e:
        print(f"无法获取股票列表，程序退出: {str(e)}")
        return
    
    if not stock_list:
        print("未能获取到任何股票信息，程序退出")
        return
    
    print(f"共获取到 {len(stock_list)} 只股票")
    
    # 下载每只股票的数据
    success_count = 0
    fail_count = 0
    failed_stocks = []
    
    with tqdm(stock_list, desc="下载进度") as pbar:
        for stock_code, stock_name in pbar:
            try:
                # 检查是否已下载
                file_path = os.path.join(base_dir, f"{stock_code}.csv")
                if os.path.exists(file_path):
                    print(f"\n股票 {stock_code} ({stock_name}) 数据已存在，跳过")
                    success_count += 1
                    continue
                
                # 获取数据
                df = download_with_backup_source(stock_code, stock_name, start_date, end_date)
                
                # 保存数据
                if df is not None and not df.empty:
                    df.to_csv(file_path, index=False)
                    print(f"\n成功保存股票 {stock_code} ({stock_name}) 的数据到 {file_path}")
                    success_count += 1
                    
                    # 记录成功日志
                    with open(log_file, "a", encoding="utf-8") as f:
                        f.write(f"{datetime.now()}: 成功下载 {stock_code} ({stock_name})\n")
                else:
                    print(f"\n获取股票 {stock_code} ({stock_name}) 数据失败")
                    fail_count += 1
                    failed_stocks.append((stock_code, stock_name))
                    
                    # 记录失败日志
                    with open(log_file, "a", encoding="utf-8") as f:
                        f.write(f"{datetime.now()}: 下载失败 {stock_code} ({stock_name})\n")
                
                # 更新进度条描述
                pbar.set_description(f"下载进度 (成功: {success_count}, 失败: {fail_count})")
                
                # 添加延时避免请求过于频繁
                time.sleep(2)
                
            except Exception as e:
                print(f"\n处理股票 {stock_code} ({stock_name}) 时发生错误: {str(e)}")
                fail_count += 1
                failed_stocks.append((stock_code, stock_name))
                
                # 记录错误日志
                with open(log_file, "a", encoding="utf-8") as f:
                    f.write(f"{datetime.now()}: 错误 {stock_code} ({stock_name}): {str(e)}\n")
                continue
    
    # 保存失败的股票列表
    if failed_stocks:
        with open(failed_stocks_file, "w", encoding="utf-8") as f:
            for code, name in failed_stocks:
                f.write(f"{code},{name}\n")
    
    print(f"\n下载完成！成功: {success_count} 只，失败: {fail_count} 只")
    print(f"详细日志已保存到: {log_file}")
    if failed_stocks:
        print(f"失败的股票列表已保存到: {failed_stocks_file}")

if __name__ == "__main__":
    main() 