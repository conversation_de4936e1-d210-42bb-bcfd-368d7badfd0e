import baostock as bs
import pandas as pd
import sqlite3
from datetime import datetime, timedelta
import time
from pathlib import Path
import logging
import os
import random
import argparse

# 配置日志
def setup_logging():
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 配置主日志文件
    main_log = log_dir / "download.log"
    
    # 创建文件处理器
    file_handler = logging.FileHandler(main_log)
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    
    # 创建控制台处理器（只显示进度相关信息）
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(message)s')
    console_handler.setFormatter(console_formatter)
    
    # 创建进度过滤器
    class ProgressFilter(logging.Filter):
        def filter(self, record):
            return "共获取" in record.msg or "数据范围" in record.msg  # 移除了 "开始处理"
    
    console_handler.addFilter(ProgressFilter())
    
    # 配置根日志记录器
    logging.root.setLevel(logging.INFO)
    logging.root.addHandler(file_handler)
    logging.root.addHandler(console_handler)
    
    # 配置错误日志文件
    error_log = logging.FileHandler(log_dir / "errors.log")
    error_log.setLevel(logging.ERROR)
    error_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    error_log.setFormatter(error_formatter)
    logging.getLogger('').addHandler(error_log)

def get_date_range(start_date='2024-09-01', end_date='2024-12-31'):
    """生成日期范围"""
    start = datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.strptime(end_date, '%Y-%m-%d')
    date_list = []
    current = start
    while current <= end:
        date_list.append(current.strftime('%Y-%m-%d'))
        current += timedelta(days=1)
    return date_list

def get_stock_progress(conn, stock_code):
    """获取指定股票的下载进度"""
    cursor = conn.cursor()
    # 确保股票代码是6位格式
    stock_code = str(stock_code).zfill(6)
    cursor.execute('''
    SELECT MAX(date) FROM stock_history 
    WHERE stock_code = ?
    ''', (stock_code,))
    last_date = cursor.fetchone()[0]
    return last_date

def get_daily_data(stock_code, date, bs_session):
    """获取单日数据"""
    try:
        # 确保股票代码是6位格式
        stock_code = str(stock_code).zfill(6)
        
        # 添加市场标识
        # 上交所：主板(600/601/603) + 科创板(688)
        # 深交所：主板(000) + 中小板(002) + 创业板(300)
        if stock_code.startswith(('600', '601', '603', '688')):
            bs_code = f"sh.{stock_code}"
        else:
            bs_code = f"sz.{stock_code}"
        
        # 获取日K线数据
        rs = bs_session.query_history_k_data_plus(
            bs_code,
            "date,code,open,high,low,close,volume,amount",
            start_date=date,
            end_date=date,
            frequency="d",
            adjustflag="3"
        )
        
        if rs.error_code != '0':
            logging.error(f"获取数据失败 - 股票: {stock_code}, 日期: {date}, 错误: {rs.error_msg}")
            return None
        
        df = rs.get_data()
        if df is not None and not df.empty:
            # 重命名列
            df.columns = ['date', 'stock_code', 'open', 'high', 'low', 'close', 'volume', 'amount']
            
            # 转换数据类型
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 保持带市场标识的股票代码
            return df
        
        return None
    except Exception as e:
        logging.error(f"处理异常 - 股票: {stock_code}, 日期: {date}, 错误: {str(e)}")
        return None

def save_to_database(df, stock_code, conn):
    """将数据保存到数据库"""
    try:
        if df is not None and not df.empty:
            df_save = df.copy()
            records = df_save.to_dict('records')
            
            cursor = conn.cursor()
            cursor.executemany('''
            INSERT OR REPLACE INTO stock_history (
                stock_code, date, open, high, low, close, volume, amount
            ) VALUES (
                :stock_code, :date, :open, :high, :low, :close, :volume, :amount
            )
            ''', records)
            
            conn.commit()
            return True
    except Exception as e:
        logging.error(f"保存数据异常 - 股票: {stock_code}, 错误: {str(e)}")
        return False

def process_single_stock(stock_code, stock_name, date_range, bs_session, conn):
    """处理单只股票的所有数据下载"""
    # 确保股票代码是6位格式
    stock_code = str(stock_code).zfill(6)
    
    # 将开始处理的信息改为 debug 级别
    logging.debug(f"开始处理股票: {stock_code} ({stock_name})")
    
    # 获取断点续传的位置
    last_date = get_stock_progress(conn, stock_code)
    if last_date:
        try:
            start_idx = date_range.index(last_date) + 1
            date_range = date_range[start_idx:]
            if date_range:
                logging.debug(f"股票 {stock_code} 将从 {date_range[0]} 继续下载")
        except ValueError:
            logging.warning(f"无法确定股票 {stock_code} 的续传位置，将重新下载")
    
    success_count = 0
    fail_count = 0
    retry_count = 0
    max_retries = 3
    
    for date in date_range:
        try:
            # 更新进度显示
            print(f"\r处理进度: {current_stock_idx}/{total_stocks_global} - 当前处理股票: {stock_code} ({stock_name}) -- {date}", 
                  end="", flush=True)
            
            df = get_daily_data(stock_code, date, bs_session)
            
            if df is not None and not df.empty:
                if save_to_database(df, stock_code, conn):
                    success_count += 1
                    retry_count = 0  # 重置重试计数
                    # 成功后减少等待时间
                    time.sleep(random.uniform(0.04, 0.06))
                else:
                    fail_count += 1
            else:
                # 对于没有数据的情况，不计入失败次数（可能是非交易日）
                logging.debug(f"股票 {stock_code} 在 {date} 没有数据")
                time.sleep(random.uniform(0.04, 0.06))
            
        except Exception as e:
            fail_count += 1
            retry_count += 1
            logging.error(f"处理异常 - 股票: {stock_code}, 日期: {date}, 错误: {str(e)}")
            
            # 如果连续失败次数过多，增加等待时间
            if retry_count >= max_retries:
                logging.warning(f"连续失败次数过多，增加等待时间")
                time.sleep(random.uniform(1.0, 2.0))
                retry_count = 0
            continue
    
    # 将完成信息写入日志文件，但不显示在终端
    logging.debug(f"股票 {stock_code} ({stock_name}) 处理完成 - 成功: {success_count}, 失败: {fail_count}")
    return success_count, fail_count

def get_global_progress(db_path):
    """获取全局下载进度"""
    conn = sqlite3.connect(str(db_path))
    try:
        cursor = conn.cursor()
        # 获取最后处理的股票
        cursor.execute('''
        SELECT DISTINCT stock_code FROM stock_history 
        ORDER BY stock_code DESC LIMIT 1
        ''')
        result = cursor.fetchone()
        if result:
            # 从带市场标识的代码中提取纯数字代码
            stock_code = result[0].split('.')[-1]
            
            # 检查该股票的数据是否完整（从1月1日到5月30日）
            cursor.execute('''
            SELECT MIN(date) as first_date, MAX(date) as last_date, COUNT(*) as count
            FROM stock_history 
            WHERE stock_code = ?
            ''', (result[0],))
            data = cursor.fetchone()
            if data:
                first_date, last_date, count = data
                logging.info(f"最后下载的股票 {stock_code}: 从 {first_date} 到 {last_date}, 共 {count} 条数据")
                
                # 如果最后日期不是5月30日，说明这只股票没下载完
                if last_date != '2025-05-30':
                    logging.info(f"股票 {stock_code} 数据不完整，将从该股票重新下载")
                    return stock_code
                else:
                    logging.info(f"股票 {stock_code} 数据已完整下载")
                    return stock_code
        return None
    finally:
        conn.close()

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='下载股票数据')
    parser.add_argument('--market', type=str, choices=['sh', 'sz'], help='市场类型：sh（上证）或 sz（深证）')
    parser.add_argument('--start_date', type=str, default='2024-09-01', help='开始日期，格式：YYYY-MM-DD')
    parser.add_argument('--end_date', type=str, default='2024-12-31', help='结束日期，格式：YYYY-MM-DD')
    args = parser.parse_args()

    # 设置日志
    setup_logging()
    
    # 连接数据库
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'stock.db')
    conn = sqlite3.connect(db_path)
    
    # 登录 baostock
    bs_session = bs.login()
    if bs_session.error_code != '0':
        logging.error(f'登录失败: {bs_session.error_msg}')
        return
        
    try:
        # 获取股票列表
        stock_list = []
        if args.market == 'sh':
            rs = bs_session.query_sh_a_stock_list()
            market_name = "上证A股"
        else:
            rs = bs_session.query_sz_a_stock_list()
            market_name = "深证A股"
            
        if rs.error_code != '0':
            logging.error(f"获取{market_name}列表失败: {rs.error_msg}")
            return
            
        stock_list = rs.get_data()
        
        # 设置全局变量用于进度显示
        global total_stocks_global
        global current_stock_idx
        total_stocks_global = len(stock_list)
        current_stock_idx = 0
        
        # 获取日期范围
        date_range = get_date_range(args.start_date, args.end_date)
        logging.info(f"数据范围: {args.start_date} 至 {args.end_date}")
        logging.info(f"共获取 {market_name} {len(stock_list)} 只股票")
        
        # 处理每只股票
        for _, row in stock_list.iterrows():
            current_stock_idx += 1
            process_single_stock(row['code'], row['code_name'], date_range, bs_session, conn)
            
    finally:
        bs.logout()
        conn.close()

if __name__ == '__main__':
    main() 