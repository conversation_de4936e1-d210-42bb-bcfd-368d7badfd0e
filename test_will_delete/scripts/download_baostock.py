import baostock as bs
import pandas as pd
import sqlite3
from datetime import datetime, timedelta
import time
from pathlib import Path
from tqdm import tqdm
import os
import random

def get_date_range(end_date_str='20240529', days=365):
    """
    生成日期范围
    """
    end_date = datetime.strptime(end_date_str, '%Y%m%d')
    date_list = []
    for i in range(days):
        current_date = end_date - timedelta(days=i)
        date_list.append(current_date.strftime('%Y-%m-%d'))
    return date_list

def get_stock_list_from_db(conn):
    """
    从本地数据库获取股票列表
    """
    try:
        cursor = conn.cursor()
        cursor.execute('''
        SELECT DISTINCT stock_code, stock_name 
        FROM stocks 
        ORDER BY stock_code
        ''')
        # 确保股票代码是字符串格式
        return [(str(code).zfill(6), name) for code, name in cursor.fetchall()]
    except Exception as e:
        print(f"从数据库获取股票列表时出错: {str(e)}")
        return None

def get_daily_data(stock_code, date, bs_session=None):
    """
    获取指定股票在指定日期的数据
    bs_session: 可选的baostock会话，如果不提供则创建新会话
    """
    need_logout = False
    try:
        # 确保股票代码是6位字符串
        stock_code = str(stock_code).zfill(6)
        
        # 如果没有提供会话，则登录
        if bs_session is None:
            bs_session = bs
            lg = bs_session.login()
            if lg.error_code != '0':
                print(f'登录失败: {lg.error_msg}')
                return None
            need_logout = True
            
        # 添加市场标识
        bs_code = f"sh.{stock_code}" if stock_code.startswith('6') else f"sz.{stock_code}"
        
        # 获取数据
        rs = bs_session.query_history_k_data_plus(
            bs_code,
            "date,open,high,low,close,volume,amount,turn,pctChg",
            start_date=date,
            end_date=date,
            frequency="d",
            adjustflag="3"  # 复权类型：3表示不复权
        )
        
        if rs.error_code != '0':
            return None
            
        # 转换为DataFrame
        data_list = []
        while (rs.error_code == '0') & rs.next():
            data_list.append(rs.get_row_data())
            
        if data_list:
            df = pd.DataFrame(data_list, columns=rs.fields)
            # 转换数据类型
            for col in ['open', 'high', 'low', 'close', 'volume', 'amount', 'turn', 'pctChg']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            return df
            
        return None
    finally:
        if need_logout:
            bs_session.logout()

def save_to_database(df, stock_code, stock_name, market_type, conn):
    """
    将数据保存到数据库
    """
    try:
        if df is not None and not df.empty:
            # 创建新的DataFrame而不是视图
            df_save = df[['date', 'open', 'high', 'low', 'close', 'volume', 'amount']].copy()
            
            # 添加股票代码
            df_save['stock_code'] = stock_code
            
            # 将数据转换为列表
            records = df_save.to_dict('records')
            
            # 批量插入数据
            cursor = conn.cursor()
            cursor.executemany('''
            INSERT OR REPLACE INTO stock_history (
                stock_code, date, open, high, low, close, volume, amount
            ) VALUES (
                :stock_code, :date, :open, :high, :low, :close, :volume, :amount
            )
            ''', records)
            
            conn.commit()
            return True
    except Exception as e:
        print(f"\n保存数据时出错: {str(e)}")
        return False

def get_progress(conn):
    """
    获取已下载的最新日期和进度
    返回: (最新日期, {日期: {已完成股票代码集合}})
    """
    try:
        cursor = conn.cursor()
        # 获取最新日期
        cursor.execute('SELECT DISTINCT date FROM stock_history ORDER BY date DESC LIMIT 1')
        last_date = cursor.fetchone()
        if not last_date:
            return None, {}
            
        # 获取每个日期的完成情况
        cursor.execute('SELECT date, stock_code FROM stock_history')
        progress = {}
        for date, code in cursor.fetchall():
            if date not in progress:
                progress[date] = set()
            progress[date].add(code)
            
        return last_date[0], progress
    except Exception as e:
        print(f"获取进度时出错: {str(e)}")
        return None, {}

def main(batch_size=200, request_interval=(0.2, 0.3), batch_interval=(1, 2)):
    """
    主函数
    batch_size: 每批处理的股票数量
    request_interval: 每次请求间隔时间范围（秒）
    batch_interval: 每批数据处理完后的间隔时间范围（秒）
    """
    # 连接数据库
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    
    try:
        # 从数据库获取股票列表
        print("从数据库获取股票列表...")
        stock_list = get_stock_list_from_db(conn)
        
        if not stock_list:
            print("无法获取股票列表，程序退出")
            return
        
        total_stocks = len(stock_list)
        print(f"共获取到 {total_stocks} 只股票")
        
        # 获取日期范围
        dates = get_date_range()
        
        # 检查断点续传
        last_date, progress = get_progress(conn)
        if last_date:
            print(f"发现已下载数据，最新日期为: {last_date}")
            # 找到上次下载的位置
            try:
                start_idx = dates.index(last_date)
                # 如果当天未完成，继续当天
                if len(progress.get(last_date, set())) < total_stocks:
                    start_idx -= 1
                # 从下一天开始下载
                dates = dates[start_idx + 1:]
                print(f"将从 {dates[0] if dates else last_date} 继续下载")
            except ValueError:
                print("无法确定续传位置，将重新下载")
        
        if not dates and (not last_date or len(progress.get(last_date, set())) >= total_stocks):
            print("没有需要下载的数据，程序退出")
            return
            
        # 登录baostock（整个过程使用同一个会话）
        print("\n登录baostock...")
        bs_session = bs
        lg = bs_session.login()
        if lg.error_code != '0':
            print(f'登录失败: {lg.error_msg}')
            return
            
        try:
            # 对每个日期
            for date in dates:
                success_count = 0
                skip_count = 0
                fail_count = 0
                processed_count = 0
                error_stocks = []  # 记录异常的股票
                
                # 获取已完成的股票
                completed_stocks = progress.get(date, set())
                remaining_stocks = [(code, name) for code, name in stock_list if code not in completed_stocks]
                
                if not remaining_stocks:
                    print(f"\n日期 {date} 已完成下载，共 {total_stocks}/{total_stocks} 只股票")
                    continue
                
                print(f"\n开始下载 {date} 的数据，剩余 {len(remaining_stocks)} 只股票")
                
                # 按批次处理股票
                for i in range(0, len(remaining_stocks), batch_size):
                    batch = remaining_stocks[i:i+batch_size]
                    batch_error_stocks = []  # 记录本批次的异常股票
                    
                    # 处理每批股票
                    for stock_code, stock_name in batch:
                        try:
                            # 检查是否已存在数据
                            cursor = conn.cursor()
                            cursor.execute('''
                            SELECT 1 FROM stock_history 
                            WHERE date = ? AND stock_code = ?
                            ''', (date, stock_code))
                            
                            if cursor.fetchone():
                                skip_count += 1
                                processed_count += 1
                                continue
                            
                            # 获取数据
                            df = get_daily_data(stock_code, date, bs_session)
                            
                            if df is not None and not df.empty:
                                # 确定市场类型
                                market_type = "沪市" if stock_code.startswith('6') else "深市"
                                
                                # 保存到数据库
                                if save_to_database(df, stock_code, stock_name, market_type, conn):
                                    success_count += 1
                                else:
                                    fail_count += 1
                                    error_stocks.append((stock_code, stock_name, "保存失败"))
                                    batch_error_stocks.append((stock_code, stock_name, "保存失败"))
                            else:
                                fail_count += 1
                                error_stocks.append((stock_code, stock_name, "无数据"))
                                batch_error_stocks.append((stock_code, stock_name, "无数据"))
                            
                            processed_count += 1
                            
                            # 使用足够长的空格清除整行
                            print(f"\r{' ' * 100}", end="", flush=True)  # 清除整行
                            print(f"\r下载数据 {date}，进度 {processed_count}/{total_stocks}，"
                                  f"成功 {success_count}，跳过 {skip_count}，"
                                  f"异常 {fail_count}", end="", flush=True)
                            
                            # 请求间隔
                            time.sleep(random.uniform(*request_interval))
                            
                        except Exception as e:
                            fail_count += 1
                            error_msg = f"异常: {str(e)}"
                            error_stocks.append((stock_code, stock_name, error_msg))
                            batch_error_stocks.append((stock_code, stock_name, error_msg))
                            processed_count += 1
                            continue
                    
                    # 显示本批次的异常详情
                    if batch_error_stocks:
                        print("\n\n本批次异常股票:")
                        for code, name, reason in batch_error_stocks:
                            print(f"  {code} ({name}): {reason}")
                        print(f"\n继续下载 {date} 的数据...")
                    
                    # 批次间隔
                    if i + batch_size < len(remaining_stocks):
                        time.sleep(random.uniform(*batch_interval))
                
                # 换行，开始新的日期
                print(f"\n{date} 处理完成，成功 {success_count}，跳过 {skip_count}，异常 {fail_count}")
                
                # 如果有异常，显示汇总信息
                if error_stocks:
                    print("\n异常股票汇总:")
                    error_types = {}
                    for _, _, reason in error_stocks:
                        error_types[reason] = error_types.get(reason, 0) + 1
                    for reason, count in error_types.items():
                        print(f"  {reason}: {count}只")
            
        finally:
            # 确保登出
            bs_session.logout()
        
    except Exception as e:
        print(f"\n程序执行出错: {str(e)}")
        raise  # 抛出异常，方便查看完整的错误信息
    finally:
        conn.close()

if __name__ == "__main__":
    # 每批200只股票
    # 每次请求间隔0.2-0.3秒
    # 每批数据间隔1-2秒
    main(batch_size=200, request_interval=(0.2, 0.3), batch_interval=(1, 2)) 