import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
import time

# 测试美股
stock = 'AAPL'  # 苹果公司

# 设置时间范围
end_date = datetime.now()
start_date = end_date - timedelta(days=30)  # 获取最近30天的数据

print(f"\n获取股票 {stock} 的日线数据 (从 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})...")

try:
    print(f"\n尝试获取股票 {stock} 的数据...")
    
    # 获取股票信息
    ticker = yf.Ticker(stock)
    
    # 获取历史数据
    df = ticker.history(
        start=start_date,
        end=end_date,
        interval='1d'  # 日线数据
    )
    
    if df is not None and not df.empty:
        print(f"成功获取股票 {stock} 的数据")
        print("\n数据字段:", df.columns.tolist())
        print("\n数据统计信息:")
        print(df.describe())
        print("\n数据前5行:")
        print(df.head())
        
        # 保存数据到 CSV 文件
        csv_file = f"{stock}_data.csv"
        df.to_csv(csv_file)
        print(f"\n数据已保存到文件: {csv_file}")
    else:
        print(f"获取股票 {stock} 数据时返回空数据")
        
except Exception as e:
    print(f"获取股票 {stock} 数据时出错: {str(e)}") 