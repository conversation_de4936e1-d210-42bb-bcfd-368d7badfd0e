from emtl import EmQuantAPI

# 初始化API
api = EmQuantAPI()

# 测试用的股票代码列表
test_stocks = ['600519', '000001', '601318', '600036', '000858']  # 茅台、平安银行、中国平安、招商银行、五粮液

print("\n获取示例股票的日线数据...")
for stock in test_stocks:
    try:
        # 获取数据
        df = api.get_history_data(stock, start_date='20240101', end_date='20240331')
        print(f"\n股票代码 {stock} 的数据示例:")
        print("数据字段:", df.columns.tolist())
        print("数据前5行:")
        print(df.head())
    except Exception as e:
        print(f"获取股票 {stock} 数据时出错: {str(e)}")

# 关闭API连接
api.close() 