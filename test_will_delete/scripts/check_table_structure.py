import sqlite3
from pathlib import Path

def check_table_structure():
    """检查数据库表结构"""
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    
    try:
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("""
        SELECT name 
        FROM sqlite_master 
        WHERE type='table'
        """)
        tables = cursor.fetchall()
        
        print("\n=== 数据库表结构检查 ===")
        print(f"\n发现 {len(tables)} 个表:")
        
        # 检查每个表的结构
        for (table_name,) in tables:
            print(f"\n表 {table_name} 的结构:")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            for col in columns:
                print(f"  列名: {col[1]}")
                print(f"    类型: {col[2]}")
                print(f"    可空: {'是' if col[3] == 0 else '否'}")
                print(f"    默认值: {col[4]}")
                print(f"    主键: {'是' if col[5] == 1 else '否'}")
            
            # 获取表的记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"\n  总记录数: {count}")
            
            # 显示示例数据
            cursor.execute(f"SELECT * FROM {table_name} LIMIT 1")
            example = cursor.fetchone()
            if example:
                print("  示例数据:")
                for col, val in zip([col[1] for col in columns], example):
                    print(f"    {col}: {val}")
        
    finally:
        conn.close()

if __name__ == "__main__":
    check_table_structure() 