import sqlite3
from pathlib import Path

def check_duplicates():
    """检查数据库中的重复记录"""
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    
    try:
        cursor = conn.cursor()
        
        # 检查stock_history表中的重复记录
        cursor.execute("""
        SELECT h1.stock_code, h1.date, COUNT(*) as count
        FROM stock_history h1
        GROUP BY h1.stock_code, h1.date
        HAVING COUNT(*) > 1
        ORDER BY h1.stock_code, h1.date
        """)
        
        duplicates = cursor.fetchall()
        
        print("\n=== 重复数据检查报告 ===")
        if duplicates:
            print(f"\n发现 {len(duplicates)} 组重复记录:")
            for record in duplicates:
                print(f"\n股票代码: {record[0]}, 日期: {record[1]}, 重复次数: {record[2]}")
                
                # 显示重复记录的详细信息
                cursor.execute("""
                SELECT stock_code, date, open, high, low, close, volume, amount
                FROM stock_history
                WHERE stock_code = ? AND date = ?
                """, (record[0], record[1]))
                
                details = cursor.fetchall()
                print("详细数据:")
                for i, detail in enumerate(details, 1):
                    print(f"{i}. 开盘: {detail[2]}, 收盘: {detail[5]}, 成交量: {detail[6]}")
        else:
            print("\n未发现重复记录")
            
        # 检查stocks表中的重复记录
        cursor.execute("""
        SELECT stock_code, COUNT(*) as count
        FROM stocks
        GROUP BY stock_code
        HAVING COUNT(*) > 1
        """)
        
        stock_duplicates = cursor.fetchall()
        if stock_duplicates:
            print(f"\n在stocks表中发现 {len(stock_duplicates)} 个重复的股票代码:")
            for record in stock_duplicates:
                print(f"股票代码: {record[0]}, 重复次数: {record[1]}")
        
    finally:
        conn.close()

if __name__ == "__main__":
    check_duplicates() 