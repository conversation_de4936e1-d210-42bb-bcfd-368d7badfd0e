import requests
import pandas as pd
from datetime import datetime, timedelta
import json
import time

def get_sse_stock_data(stock_code):
    """
    从上海证券交易所获取股票数据
    stock_code: 股票代码（不带市场标识）
    """
    # SSE API URL (新版接口)
    url = f"http://yunhq.sse.com.cn:32041/v1/sh1/line/{stock_code}"
    
    # 请求头
    headers = {
        "Referer": "http://www.sse.com.cn/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # 检查请求是否成功
        
        data = response.json()
        
        if isinstance(data, dict) and "line" in data:
            # 处理数据
            df = pd.DataFrame(data["line"], columns=["time", "price", "volume"])
            
            # 转换时间格式
            df["time"] = df["time"].apply(lambda x: f"{int(x//100):02d}:{int(x%100):02d}")
            
            print("\n处理后的数据前5行:")
            print(df.head())
            
            # 保存数据到CSV文件
            csv_file = f"sse_{stock_code}_data.csv"
            df.to_csv(csv_file, index=False)
            print(f"\n数据已保存到文件: {csv_file}")
            
            return df
        else:
            print("数据格式不正确或为空")
            return None
        
    except Exception as e:
        print(f"获取数据时出错: {str(e)}")
        return None

def get_szse_stock_data(stock_code):
    """
    从深圳证券交易所获取股票数据
    stock_code: 股票代码（不带市场标识）
    """
    # SZSE API URL
    url = "http://www.szse.cn/api/market/ssjjhq/getTimeData"
    
    # 请求头
    headers = {
        "Referer": "http://www.szse.cn/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    
    # 请求参数
    params = {
        "marketId": "1",
        "code": stock_code
    }
    
    try:
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()  # 检查请求是否成功
        
        data = response.json()
        
        # 处理数据
        if data.get("code") == "0" and data.get("data"):
            df = pd.DataFrame({
                "time": [x[0] for x in data["data"]["picupdata"]],
                "price": [x[1] for x in data["data"]["picupdata"]],
                "volume": [x[2] for x in data["data"]["picupdata"]]
            })
            print("\n处理后的数据前5行:")
            print(df.head())
            
            # 保存数据到CSV文件
            csv_file = f"szse_{stock_code}_data.csv"
            df.to_csv(csv_file, index=False)
            print(f"\n数据已保存到文件: {csv_file}")
            
            return df
        else:
            print("数据格式不正确或为空")
            return None
        
    except Exception as e:
        print(f"获取数据时出错: {str(e)}")
        return None

def main():
    # 测试上证股票
    print("\n测试上海证券交易所数据获取:")
    sse_stock = "600519"  # 贵州茅台
    sse_data = get_sse_stock_data(sse_stock)

    # 等待一段时间再请求下一个数据
    time.sleep(5)

    # 测试深证股票
    print("\n测试深圳证券交易所数据获取:")
    szse_stock = "000001"  # 平安银行
    szse_data = get_szse_stock_data(szse_stock)

if __name__ == "__main__":
    main() 