#!/bin/bash

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# 创建日志目录
mkdir -p "$PROJECT_ROOT/logs"

# 启动历史数据下载（上证）
nohup python3 "$SCRIPT_DIR/download_by_stock.py" --market sh --start_date 2024-09-01 --end_date 2024-12-31 > "$PROJECT_ROOT/logs/download_sh_$(date +%Y%m%d).log" 2>&1 &
echo "启动上证历史数据下载任务，进程ID: $!"

# 等待5秒后启动深证数据下载
sleep 5

# 启动历史数据下载（深证）
nohup python3 "$SCRIPT_DIR/download_by_stock.py" --market sz --start_date 2024-09-01 --end_date 2024-12-31 > "$PROJECT_ROOT/logs/download_sz_$(date +%Y%m%d).log" 2>&1 &
echo "启动深证历史数据下载任务，进程ID: $!"

# 等待5秒后启动每日更新服务
sleep 5

# 启动每日更新服务
nohup python3 "$SCRIPT_DIR/daily_update.py" > "$PROJECT_ROOT/logs/daily_update_$(date +%Y%m%d).log" 2>&1 &
echo "启动每日更新服务，进程ID: $!"

echo "所有任务已启动，日志文件保存在 $PROJECT_ROOT/logs 目录下" 