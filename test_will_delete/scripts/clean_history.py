import sqlite3
from pathlib import Path
import logging

def setup_logging():
    """配置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def clean_history():
    """清理股票历史数据表"""
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    
    try:
        cursor = conn.cursor()
        
        # 1. 获取当前记录数
        cursor.execute("SELECT COUNT(*) FROM stock_history")
        count_before = cursor.fetchone()[0]
        print(f"\n当前stock_history表中有 {count_before} 条记录")
        
        # 2. 清空表
        cursor.execute("DELETE FROM stock_history")
        
        # 3. 提交更改
        conn.commit()
        
        # 4. 验证清理结果
        cursor.execute("SELECT COUNT(*) FROM stock_history")
        count_after = cursor.fetchone()[0]
        print(f"清理后stock_history表中有 {count_after} 条记录")
        
        print("\n清理完成")
        
    finally:
        conn.close()

if __name__ == "__main__":
    setup_logging()
    clean_history() 