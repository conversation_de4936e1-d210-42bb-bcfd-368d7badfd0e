import sqlite3
from pathlib import Path
import logging
import baostock as bs

def setup_logging():
    """配置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def init_database():
    """初始化数据库表"""
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    
    try:
        cursor = conn.cursor()
        
        # 创建股票基本信息表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS stocks (
            stock_code TEXT PRIMARY KEY,
            stock_name TEXT,
            listing_date TEXT,
            delisting_date TEXT
        )
        ''')
        
        # 创建股票历史数据表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS stock_history (
            stock_code TEXT,
            date TEXT,
            open REAL,
            high REAL,
            low REAL,
            close REAL,
            volume REAL,
            amount REAL,
            PRIMARY KEY (stock_code, date)
        )
        ''')
        
        # 获取股票列表
        bs.login()
        try:
            rs = bs.query_stock_basic()
            stock_list = []
            while (rs.error_code == '0') & rs.next():
                row = rs.get_row_data()
                # 只保留A股
                if row[0].startswith(('sh.6', 'sz.000', 'sz.002', 'sz.300')):
                    stock_code = row[0].split('.')[-1]
                    stock_name = row[1]
                    listing_date = row[2]
                    delisting_date = row[3] if row[3] != '' else None
                    stock_list.append((stock_code, stock_name, listing_date, delisting_date))
            
            # 插入股票信息
            cursor.executemany('''
            INSERT OR REPLACE INTO stocks (stock_code, stock_name, listing_date, delisting_date)
            VALUES (?, ?, ?, ?)
            ''', stock_list)
            
            print(f"\n成功导入 {len(stock_list)} 只股票的基本信息")
            
        finally:
            bs.logout()
        
        # 提交更改
        conn.commit()
        print("\n数据库初始化完成")
        
    finally:
        conn.close()

if __name__ == "__main__":
    setup_logging()
    init_database() 