import sqlite3
from pathlib import Path
import logging

def setup_logging():
    """配置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def check_data():
    """检查特定股票的数据"""
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    
    try:
        cursor = conn.cursor()
        
        # 1. 检查stocks表中的数据
        print("\n=== stocks表数据 ===")
        cursor.execute("""
        SELECT stock_code, stock_name 
        FROM stocks 
        WHERE stock_code IN ('000001', '000002', '000003')
        """)
        
        stocks = cursor.fetchall()
        for stock in stocks:
            print(f"\n股票代码: {stock[0]}")
            print(f"股票名称: {stock[1]}")
        
        # 2. 检查stock_history表中的数据
        print("\n=== stock_history表数据 ===")
        for stock_code in ['000001', '000002', '000003']:
            cursor.execute("""
            SELECT COUNT(*) 
            FROM stock_history 
            WHERE stock_code = ?
            """, (stock_code,))
            count = cursor.fetchone()[0]
            
            print(f"\n股票代码 {stock_code}:")
            print(f"- 记录数量: {count}")
            
            if count > 0:
                # 显示最新的3条记录
                cursor.execute("""
                SELECT date, stock_code, open, close, volume
                FROM stock_history
                WHERE stock_code = ?
                ORDER BY date DESC
                LIMIT 3
                """, (stock_code,))
                
                records = cursor.fetchall()
                print("最新记录:")
                for record in records:
                    print(f"  日期: {record[0]}, 股票代码: {record[1]}, "
                          f"开盘: {record[2]}, 收盘: {record[3]}, 成交量: {record[4]}")
        
    finally:
        conn.close()

if __name__ == "__main__":
    setup_logging()
    check_data() 