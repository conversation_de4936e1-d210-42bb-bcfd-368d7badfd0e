import sqlite3
from pathlib import Path
import logging
import baostock as bs
import pandas as pd
import time
import random
from datetime import datetime, timedelta
import argparse

def setup_logging():
    """配置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def get_date_range(start_date='2025-01-01', end_date='2025-05-30'):
    """生成日期范围"""
    start = datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.strptime(end_date, '%Y-%m-%d')
    date_list = []
    current = start
    while current <= end:
        date_list.append(current.strftime('%Y-%m-%d'))
        current += timedelta(days=1)
    return date_list

def get_daily_data(stock_code, date, bs_session):
    """获取单日数据"""
    try:
        # 如果股票代码已经包含市场标识，直接使用
        if stock_code.startswith(('sz.', 'sh.')):
            bs_code = stock_code
        else:
            # 确保股票代码是6位格式
            pure_code = str(stock_code).zfill(6)
            # 添加市场标识
            bs_code = f"sh.{pure_code}" if pure_code.startswith('6') else f"sz.{pure_code}"
        
        # 确保不会出现重复的市场标识
        if bs_code.startswith('sz.sz.') or bs_code.startswith('sh.sh.'):
            bs_code = bs_code[3:]  # 移除重复的市场标识
        
        rs = bs_session.query_history_k_data_plus(
            bs_code,
            "date,code,open,high,low,close,volume,amount",
            start_date=date,
            end_date=date,
            frequency="d",
            adjustflag="3"
        )
        
        if rs.error_code != '0':
            logging.error(f"获取数据失败 - 股票: {bs_code}, 日期: {date}, 错误: {rs.error_msg}")
            return None
        
        df = rs.get_data()
        if df is not None and not df.empty:
            df.columns = ['date', 'stock_code', 'open', 'high', 'low', 'close', 'volume', 'amount']
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            return df
        
        return None
    except Exception as e:
        logging.error(f"处理异常 - 股票: {bs_code}, 日期: {date}, 错误: {str(e)}")
        return None

def find_incomplete_stocks(market_type=None):
    """查找数据不完整的股票
    
    Args:
        market_type: 'sh' 表示上证，'sz' 表示深证，None 表示所有
    """
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    try:
        cursor = conn.cursor()
        # 基础查询
        query = '''
        SELECT 
            stock_code,
            MIN(date) as first_date,
            MAX(date) as last_date,
            COUNT(*) as record_count
        FROM stock_history
        '''
        
        # 根据市场类型添加WHERE子句
        if market_type:
            if market_type == 'sh':
                query += " WHERE stock_code LIKE '6%' OR stock_code LIKE 'sh.6%'"
            elif market_type == 'sz':
                query += " WHERE (stock_code NOT LIKE '6%' AND stock_code NOT LIKE 'sh.6%')"
        
        query += '''
        GROUP BY stock_code
        HAVING first_date > '2025-01-01' 
           OR last_date < '2025-05-30'
           OR record_count < 100
        ORDER BY stock_code
        '''
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        # 处理股票代码，添加市场标识
        processed_results = []
        for result in results:
            stock_code = str(result[0])
            # 如果已经有市场标识，直接使用
            if stock_code.startswith(('sz.', 'sh.')):
                # 根据市场类型过滤
                if market_type:
                    if (market_type == 'sh' and stock_code.startswith('sh.')) or \
                       (market_type == 'sz' and stock_code.startswith('sz.')):
                        processed_results.append(result)
                else:
                    processed_results.append(result)
            else:
                # 确保股票代码是6位格式
                pure_code = str(stock_code).zfill(6)
                is_sh = pure_code.startswith('6')
                # 根据市场类型过滤
                if market_type:
                    if (market_type == 'sh' and is_sh) or \
                       (market_type == 'sz' and not is_sh):
                        market_prefix = "sh." if is_sh else "sz."
                        processed_results.append((market_prefix + pure_code,) + result[1:])
                else:
                    market_prefix = "sh." if is_sh else "sz."
                    processed_results.append((market_prefix + pure_code,) + result[1:])
        
        return processed_results
    finally:
        conn.close()

def save_to_database(df, stock_code, conn):
    """将数据保存到数据库"""
    try:
        if df is not None and not df.empty:
            df_save = df.copy()
            records = df_save.to_dict('records')
            
            cursor = conn.cursor()
            cursor.executemany('''
            INSERT OR REPLACE INTO stock_history (
                stock_code, date, open, high, low, close, volume, amount
            ) VALUES (
                :stock_code, :date, :open, :high, :low, :close, :volume, :amount
            )
            ''', records)
            
            conn.commit()
            return True
    except Exception as e:
        logging.error(f"保存数据异常 - 股票: {stock_code}, 错误: {str(e)}")
        return False

def fix_stock_data(stock_code, bs_session, conn):
    """修复单只股票的数据"""
    date_range = get_date_range()
    success_count = 0
    fail_count = 0
    
    for date in date_range:
        try:
            df = get_daily_data(stock_code, date, bs_session)
            if df is not None and not df.empty:
                if save_to_database(df, stock_code, conn):
                    success_count += 1
                    time.sleep(random.uniform(0.04, 0.06))
                else:
                    fail_count += 1
            else:
                time.sleep(random.uniform(0.04, 0.06))
        except Exception as e:
            logging.error(f"处理异常 - 股票: {stock_code}, 日期: {date}, 错误: {str(e)}")
            fail_count += 1
            continue
    
    return success_count, fail_count

def main():
    """主函数"""
    # 设置命令行参数
    parser = argparse.ArgumentParser(description='修复股票数据下载')
    parser.add_argument('--market', type=str, choices=['sh', 'sz'], 
                      help='选择市场类型：sh表示上证，sz表示深证。不指定则处理所有')
    args = parser.parse_args()
    
    setup_logging()
    
    # 查找不完整的股票
    incomplete_stocks = find_incomplete_stocks(args.market)
    if not incomplete_stocks:
        logging.info(f"没有发现数据不完整的{args.market if args.market else ''}股票")
        return
    
    market_name = {'sh': '上证', 'sz': '深证'}.get(args.market, '所有')
    logging.info(f"发现 {len(incomplete_stocks)} 只{market_name}数据不完整的股票")
    for stock_info in incomplete_stocks:
        stock_code = stock_info[0]
        first_date = stock_info[1]
        last_date = stock_info[2]
        record_count = stock_info[3]
        logging.info(f"股票 {stock_code}: {first_date} 至 {last_date}, {record_count} 条数据")
    
    # 确认是否继续
    input("\n按回车键开始修复数据...")
    
    # 连接数据库
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    
    try:
        # 登录baostock
        logging.info("登录 baostock...")
        bs_session = bs
        lg = bs_session.login()
        if lg.error_code != '0':
            logging.error(f'登录失败: {lg.error_msg}')
            return
        
        try:
            total_success = 0
            total_fail = 0
            
            for idx, stock_info in enumerate(incomplete_stocks, 1):
                stock_code = stock_info[0]
                logging.info(f"\n[{idx}/{len(incomplete_stocks)}] 开始修复 {stock_code} 的数据")
                
                success, fail = fix_stock_data(stock_code, bs_session, conn)
                total_success += success
                total_fail += fail
                
                # 每修复3只股票后休息一下
                if idx % 3 == 0:
                    time.sleep(random.uniform(1.0, 2.0))
            
            logging.info(f"\n数据修复完成 - 总成功: {total_success}, 总失败: {total_fail}")
            
        finally:
            bs_session.logout()
            
    except Exception as e:
        logging.error(f"程序执行出错: {str(e)}")
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    main() 