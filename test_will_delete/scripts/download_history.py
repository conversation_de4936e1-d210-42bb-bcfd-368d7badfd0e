import requests
import pandas as pd
from datetime import datetime, timedelta
import json
import time
import os
from tqdm import tqdm

def get_stock_list():
    """
    获取股票列表
    返回: [(代码, 交易所), ...]
    """
    # 上证股票列表API
    sse_url = "http://query.sse.com.cn/security/stock/getStockListData.do"
    sse_headers = {
        "Referer": "http://www.sse.com.cn/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    sse_params = {
        "jsonCallBack": "jsonpCallback",  # 添加jsonp回调
        "isPagination": "true",
        "stockCode": "",
        "csrcCode": "",
        "areaName": "",
        "stockType": "1",
        "pageHelp.cacheSize": "1",
        "pageHelp.beginPage": "1",
        "pageHelp.pageSize": "2000",
        "pageHelp.pageNo": "1",
        "_": str(int(time.time() * 1000))
    }
    
    # 深证股票列表API
    szse_url = "http://www.szse.cn/api/report/ShowReport/data"
    szse_params = {
        "SHOWTYPE": "JSON",
        "CATALOGID": "1110x",
        "TABKEY": "tab1",
        "random": str(int(time.time() * 1000))
    }
    
    stock_list = []
    
    try:
        # 获取上证股票
        print("正在获取上交所股票列表...")
        sse_response = requests.get(sse_url, headers=sse_headers, params=sse_params)
        sse_response.raise_for_status()
        
        # 处理jsonp响应
        json_str = sse_response.text
        json_str = json_str.replace('jsonpCallback(', '').rstrip(');')
        sse_data = json.loads(json_str)
        
        for stock in sse_data.get("pageHelp", {}).get("data", []):
            if "SECURITY_CODE_A" in stock:
                stock_list.append((stock["SECURITY_CODE_A"], "SSE"))
        
        print(f"成功获取上交所股票数量: {len([x for x in stock_list if x[1] == 'SSE'])}")
        
        # 获取深证股票
        print("正在获取深交所股票列表...")
        szse_response = requests.get(szse_url, params=szse_params)
        szse_response.raise_for_status()
        szse_data = szse_response.json()
        
        for item in szse_data:
            if isinstance(item, dict) and "data" in item:
                for stock in item["data"]:
                    if "code" in stock:
                        stock_list.append((stock["code"], "SZSE"))
        
        print(f"成功获取深交所股票数量: {len([x for x in stock_list if x[1] == 'SZSE'])}")
            
    except requests.exceptions.RequestException as e:
        print(f"网络请求错误: {str(e)}")
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {str(e)}")
    except Exception as e:
        print(f"其他错误: {str(e)}")
    
    return stock_list

def get_sse_daily_data(stock_code, start_date, end_date, retry_count=3):
    """
    获取上交所股票的日线数据
    """
    url = "http://yunhq.sse.com.cn:32041/v1/sh1/dayk/{stock_code}"
    
    headers = {
        "Referer": "http://www.sse.com.cn/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    
    for attempt in range(retry_count):
        try:
            response = requests.get(url.format(stock_code=stock_code), headers=headers)
            response.raise_for_status()
            data = response.json()
            
            if isinstance(data, dict) and "kline" in data:
                df = pd.DataFrame(data["kline"], columns=["date", "open", "high", "low", "close", "volume", "amount"])
                df["date"] = pd.to_datetime(df["date"], format="%Y%m%d")
                df = df[(df["date"] >= start_date) & (df["date"] <= end_date)]
                return df
            
        except requests.exceptions.RequestException as e:
            print(f"获取上交所股票 {stock_code} 数据时网络错误 (尝试 {attempt + 1}/{retry_count}): {str(e)}")
        except json.JSONDecodeError as e:
            print(f"获取上交所股票 {stock_code} 数据时JSON解析错误 (尝试 {attempt + 1}/{retry_count}): {str(e)}")
        except Exception as e:
            print(f"获取上交所股票 {stock_code} 数据时其他错误 (尝试 {attempt + 1}/{retry_count}): {str(e)}")
        
        if attempt < retry_count - 1:
            sleep_time = (attempt + 1) * 5  # 递增等待时间
            print(f"等待 {sleep_time} 秒后重试...")
            time.sleep(sleep_time)
    
    return None

def get_szse_daily_data(stock_code, start_date, end_date, retry_count=3):
    """
    获取深交所股票的日线数据
    """
    url = "http://www.szse.cn/api/market/ssjjhq/getHistoryData"
    
    headers = {
        "Referer": "http://www.szse.cn/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    
    params = {
        "code": stock_code,
        "marketId": "1",
        "cycleType": "32"  # 日线数据
    }
    
    for attempt in range(retry_count):
        try:
            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data.get("code") == "0":
                df = pd.DataFrame(data["data"])
                df["date"] = pd.to_datetime(df["date"])
                df = df[(df["date"] >= start_date) & (df["date"] <= end_date)]
                return df
            
        except requests.exceptions.RequestException as e:
            print(f"获取深交所股票 {stock_code} 数据时网络错误 (尝试 {attempt + 1}/{retry_count}): {str(e)}")
        except json.JSONDecodeError as e:
            print(f"获取深交所股票 {stock_code} 数据时JSON解析错误 (尝试 {attempt + 1}/{retry_count}): {str(e)}")
        except Exception as e:
            print(f"获取深交所股票 {stock_code} 数据时其他错误 (尝试 {attempt + 1}/{retry_count}): {str(e)}")
        
        if attempt < retry_count - 1:
            sleep_time = (attempt + 1) * 5  # 递增等待时间
            print(f"等待 {sleep_time} 秒后重试...")
            time.sleep(sleep_time)
    
    return None

def download_all_stocks_data(start_date, end_date):
    """
    下载所有股票的历史数据
    """
    # 创建保存数据的目录
    base_dir = "stock_data"
    os.makedirs(base_dir, exist_ok=True)
    
    # 获取股票列表
    print("获取股票列表...")
    stock_list = get_stock_list()
    
    if not stock_list:
        print("未能获取到任何股票信息，程序退出")
        return
    
    print(f"共获取到 {len(stock_list)} 只股票")
    
    # 转换日期格式
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)
    
    # 下载每只股票的数据
    success_count = 0
    fail_count = 0
    
    with tqdm(stock_list, desc="下载进度") as pbar:
        for stock_code, exchange in pbar:
            try:
                # 创建交易所子目录
                exchange_dir = os.path.join(base_dir, exchange)
                os.makedirs(exchange_dir, exist_ok=True)
                
                # 检查是否已下载
                file_path = os.path.join(exchange_dir, f"{stock_code}.csv")
                if os.path.exists(file_path):
                    print(f"\n股票 {stock_code} 数据已存在，跳过")
                    success_count += 1
                    continue
                
                # 获取数据
                df = None
                if exchange == "SSE":
                    df = get_sse_daily_data(stock_code, start_date, end_date)
                else:
                    df = get_szse_daily_data(stock_code, start_date, end_date)
                
                # 保存数据
                if df is not None and not df.empty:
                    df.to_csv(file_path, index=False)
                    print(f"\n成功保存股票 {stock_code} 的数据到 {file_path}")
                    success_count += 1
                else:
                    print(f"\n获取股票 {stock_code} 数据失败")
                    fail_count += 1
                
                # 更新进度条描述
                pbar.set_description(f"下载进度 (成功: {success_count}, 失败: {fail_count})")
                
                # 添加延时避免请求过于频繁
                time.sleep(2)  # 增加延时到2秒
                
            except Exception as e:
                print(f"\n处理股票 {stock_code} 时发生错误: {str(e)}")
                fail_count += 1
                continue
    
    print(f"\n下载完成！成功: {success_count} 只，失败: {fail_count} 只")

def main():
    # 设置日期范围
    start_date = "2024-01-01"
    end_date = "2024-05-30"
    
    print(f"开始下载从 {start_date} 到 {end_date} 的股票数据...")
    download_all_stocks_data(start_date, end_date)

if __name__ == "__main__":
    main() 