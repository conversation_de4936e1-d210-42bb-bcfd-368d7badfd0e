# 股票数据下载服务使用说明

## 环境要求
- Python 3.x
- 虚拟环境 (venv)
- 必需的 Python 包 (已在 requirements.txt 中列出)：
  - requests>=2.31.0
  - pandas>=2.0.0
  - tqdm>=4.66.0
  - baostock>=0.8.8

## 服务器信息
- 服务器地址：**************
- 部署路径：/root/backtrader/data/scripts
- 数据库路径：/root/backtrader/data/scripts/stock_data.db
- 日志路径：/root/backtrader/data/scripts/download_sh.log (上证) 或 download_sz.log (深证)

## 使用方法

### 1. 初始化数据库（首次使用时）
```bash
cd /root/backtrader/data/scripts
source venv/bin/activate
python init_db.py
```

### 2. 启动下载任务

#### 前台运行（不推荐）
```bash
cd /root/backtrader/data/scripts
./start.sh [sh|sz]  # sh 为上证，sz 为深证
```

#### 后台运行（推荐）
```bash
# 下载上证股票数据
cd /root/backtrader/data/scripts
nohup ./start.sh sh > download_sh.log 2>&1 &

# 下载深证股票数据
cd /root/backtrader/data/scripts
nohup ./start.sh sz > download_sz.log 2>&1 &
```

### 3. 查看下载进度
```bash
# 查看上证下载进度
tail -f /root/backtrader/data/scripts/download_sh.log

# 查看深证下载进度
tail -f /root/backtrader/data/scripts/download_sz.log
```

### 4. 检查进程状态
```bash
ps aux | grep 'start.sh'
```

### 5. 停止下载任务
```bash
# 查找进程 ID
ps aux | grep 'start.sh'

# 终止进程
kill <进程ID>
```

## 注意事项
1. 下载任务支持断点续传，中断后重新运行会从上次中断处继续
2. 数据下载过程中会自动处理网络异常和重试
3. 所有错误和异常都会记录在日志文件中
4. 建议使用后台运行方式启动任务，这样可以在断开 SSH 连接后继续运行

## 数据说明
- 上证股票：包括主板（600/601/603）和科创板（688）
- 深证股票：包括主板（000）、中小板（002）和创业板（300）
- 数据时间范围：2015-01-01 至今
- 数据频率：日线数据
- 数据存储：SQLite 数据库（stock_data.db）

## 常见问题处理
1. 如果提示 "no such table: stocks"，需要先运行 init_db.py 初始化数据库
2. 如果提示模块未找到，请检查虚拟环境是否激活，并重新安装 requirements.txt 中的依赖
3. 如果下载速度过慢，可能是网络问题或者服务器限制，建议稍后重试 