"""
此脚本用于获取和检查A股市场所有股票的基本信息。

主要功能：
1. 使用 efinance 库获取所有A股的实时行情数据
2. 检查并展示数据的结构信息（数据类型、形状、列名等）
3. 显示数据的统计信息
4. 展示样本数据（前5行）
5. 按市场类型统计股票分布
6. 将完整的股票列表数据保存为 CSV 文件 (stock_list_full.csv)

输出文件：
- stock_list_full.csv：包含所有股票的详细信息，包括：
  - 股票代码
  - 股票名称
  - 市场类型
  - 最新价
  - 涨跌幅
  - 成交量
  - 成交额
  等实时行情数据

使用场景：
- 获取最新的A股股票列表
- 分析股票市场结构
- 为其他数据下载脚本提供股票清单
- 监控市场整体情况
"""

import efinance as ef
import pandas as pd

def examine_stock_list():
    """
    检查股票列表的数据结构和内容
    """
    try:
        # 获取所有A股的股票信息
        stock_df = ef.stock.get_realtime_quotes()
        
        # 显示数据结构信息
        print("\n数据结构信息:")
        print("=" * 50)
        print(f"数据类型: {type(stock_df)}")
        print(f"数据形状: {stock_df.shape}")
        print(f"\n所有列名:")
        print(stock_df.columns.tolist())
        
        # 显示数据统计信息
        print("\n数据统计信息:")
        print("=" * 50)
        print(stock_df.info())
        
        # 显示前5行数据
        print("\n前5行数据示例:")
        print("=" * 50)
        print(stock_df.head())
        
        # 按市场分类统计
        if '市场类型' in stock_df.columns:
            print("\n按市场类型统计:")
            print("=" * 50)
            print(stock_df['市场类型'].value_counts())
        
        # 保存完整数据到CSV文件以供查看
        csv_file = "stock_list_full.csv"
        stock_df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        print(f"\n完整数据已保存到: {csv_file}")
        
    except Exception as e:
        print(f"获取股票列表时出错: {str(e)}")

if __name__ == "__main__":
    examine_stock_list() 