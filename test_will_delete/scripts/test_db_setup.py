import sqlite3
from pathlib import Path

def setup_test_db():
    """设置测试数据库"""
    db_path = Path('stock_data.db')
    conn = sqlite3.connect(str(db_path))
    
    try:
        cursor = conn.cursor()
        
        # 创建股票表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS stocks (
            code TEXT PRIMARY KEY,
            name TEXT,
            market TEXT
        )
        ''')
        
        # 插入测试数据
        cursor.execute('''
        INSERT OR REPLACE INTO stocks (code, name, market)
        VALUES (?, ?, ?)
        ''', ('300999', '金龙鱼', '深证'))
        
        conn.commit()
        print("测试数据设置完成")
        
    finally:
        conn.close()

if __name__ == "__main__":
    setup_test_db() 