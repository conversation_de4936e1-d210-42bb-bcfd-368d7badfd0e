import sqlite3
from pathlib import Path
import logging

def setup_logging():
    """配置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def import_stocks():
    """从stock_list_full导入股票列表到stocks表"""
    db_path = Path('stock_data.db')
    conn = sqlite3.connect(str(db_path))
    
    try:
        cursor = conn.cursor()
        
        # 创建stocks表（如果不存在）
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS stocks (
            code TEXT PRIMARY KEY,
            name TEXT,
            market TEXT
        )
        ''')
        
        # 从stock_list_full读取上证股票
        cursor.execute('''
        INSERT OR REPLACE INTO stocks (code, name, market)
        SELECT code, name, '上证' as market
        FROM stock_list_full
        WHERE code LIKE '60%' OR code LIKE '61%' OR code LIKE '68%'
        ''')
        
        # 获取插入的上证股票数量
        cursor.execute('SELECT COUNT(*) FROM stocks WHERE market = "上证"')
        sh_count = cursor.fetchone()[0]
        logging.info(f"已导入 {sh_count} 只上证股票")
        
        # 从stock_list_full读取深证股票
        cursor.execute('''
        INSERT OR REPLACE INTO stocks (code, name, market)
        SELECT code, name, '深证' as market
        FROM stock_list_full
        WHERE code LIKE '00%' OR code LIKE '30%'
        ''')
        
        # 获取插入的深证股票数量
        cursor.execute('SELECT COUNT(*) FROM stocks WHERE market = "深证"')
        sz_count = cursor.fetchone()[0]
        logging.info(f"已导入 {sz_count} 只深证股票")
        
        # 显示一些示例数据
        cursor.execute('''
        SELECT market, code, name 
        FROM stocks 
        GROUP BY market 
        LIMIT 10
        ''')
        samples = cursor.fetchall()
        logging.info("\n示例数据:")
        for sample in samples:
            logging.info(f"{sample[0]}: {sample[1]} {sample[2]}")
        
        conn.commit()
        logging.info(f"\n总计导入 {sh_count + sz_count} 只股票")
        
    finally:
        conn.close()

if __name__ == "__main__":
    setup_logging()
    import_stocks() 