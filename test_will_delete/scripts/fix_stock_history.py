import sqlite3
from pathlib import Path
import logging

def setup_logging():
    """配置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def fix_stock_history():
    """修复股票历史数据中的股票代码格式"""
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    
    try:
        cursor = conn.cursor()
        
        # 1. 获取所有不同的股票代码
        cursor.execute("""
        SELECT DISTINCT stock_code 
        FROM stock_history
        """)
        stock_codes = [row[0] for row in cursor.fetchall()]
        
        logging.info(f"发现 {len(stock_codes)} 个不同的股票代码")
        
        # 2. 创建临时表
        cursor.execute("""
        CREATE TABLE stock_history_temp (
            stock_code TEXT NOT NULL,
            date TEXT NOT NULL,
            open REAL,
            high REAL,
            low REAL,
            close REAL,
            volume REAL,
            amount REAL,
            ma5 REAL,
            ma10 REAL,
            ma20 REAL,
            ma30 REAL,
            ma60 REAL,
            PRIMARY KEY (stock_code, date)
        )
        """)
        
        # 3. 修复数据并插入临时表
        fixed_count = 0
        for old_code in stock_codes:
            # 生成新的6位股票代码
            new_code = str(old_code).zfill(6)
            
            # 获取该股票代码的记录数
            cursor.execute("""
            SELECT COUNT(*) 
            FROM stock_history 
            WHERE stock_code = ?
            """, (old_code,))
            record_count = cursor.fetchone()[0]
            
            logging.info(f"处理股票代码: {old_code} -> {new_code} (共 {record_count} 条记录)")
            
            # 复制数据到临时表，同时修改股票代码
            # 使用 INSERT OR REPLACE 来处理可能的重复数据
            cursor.execute("""
            INSERT OR REPLACE INTO stock_history_temp
            SELECT 
                CASE 
                    WHEN length(stock_code) < 6 
                    THEN substr('000000' || stock_code, -6)
                    ELSE stock_code 
                END as stock_code,
                date, open, high, low, close, volume, amount,
                ma5, ma10, ma20, ma30, ma60
            FROM stock_history
            WHERE stock_code = ?
            """, (old_code,))
            
            if old_code != new_code:
                fixed_count += 1
        
        # 4. 删除原表并重命名临时表
        cursor.execute("DROP TABLE stock_history")
        cursor.execute("ALTER TABLE stock_history_temp RENAME TO stock_history")
        
        # 5. 提交更改
        conn.commit()
        
        logging.info(f"修复完成，共修复 {fixed_count} 个股票代码")
        
        # 6. 验证结果
        cursor.execute("""
        SELECT stock_code, COUNT(*) as count
        FROM stock_history
        GROUP BY stock_code
        ORDER BY count DESC
        LIMIT 5
        """)
        
        print("\n示例数据:")
        for code, count in cursor.fetchall():
            print(f"股票代码: {code}, 记录数: {count}")
        
    finally:
        conn.close()

if __name__ == "__main__":
    setup_logging()
    fix_stock_history() 