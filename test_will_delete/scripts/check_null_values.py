import sqlite3
from pathlib import Path

def check_null_values():
    """检查stocks表中的NULL值"""
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    
    try:
        cursor = conn.cursor()
        
        # 获取表结构
        cursor.execute("PRAGMA table_info(stocks)")
        columns = [col[1] for col in cursor.fetchall()]
        
        print("\n=== NULL值检查报告 ===")
        
        # 检查每个列的NULL值数量
        for column in columns:
            cursor.execute(f"""
            SELECT COUNT(*) 
            FROM stocks 
            WHERE {column} IS NULL
            """)
            null_count = cursor.fetchone()[0]
            
            if null_count > 0:
                print(f"\n列 {column}:")
                print(f"- NULL值数量: {null_count}")
                
                # 显示一些包含NULL的记录示例
                cursor.execute(f"""
                SELECT stock_code, stock_name, {column}
                FROM stocks
                WHERE {column} IS NULL
                LIMIT 3
                """)
                examples = cursor.fetchall()
                print("示例记录:")
                for ex in examples:
                    print(f"  股票: {ex[0]} ({ex[1]}), 值: {ex[2]}")
        
    finally:
        conn.close()

if __name__ == "__main__":
    check_null_values() 