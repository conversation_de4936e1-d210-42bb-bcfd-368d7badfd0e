import sqlite3
from pathlib import Path

def check_db_structure():
    """检查数据库结构和示例数据"""
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    
    try:
        cursor = conn.cursor()
        
        # 1. 检查表结构
        print("\n=== 数据库表结构 ===")
        
        # stocks表结构
        cursor.execute("PRAGMA table_info(stocks)")
        print("\nstocks表结构:")
        for col in cursor.fetchall():
            print(f"列名: {col[1]}, 类型: {col[2]}, 是否可空: {col[3]}, 默认值: {col[4]}, 是否主键: {col[5]}")
            
        # stock_history表结构
        cursor.execute("PRAGMA table_info(stock_history)")
        print("\nstock_history表结构:")
        for col in cursor.fetchall():
            print(f"列名: {col[1]}, 类型: {col[2]}, 是否可空: {col[3]}, 默认值: {col[4]}, 是否主键: {col[5]}")
            
        # 2. 检查索引
        print("\n=== 索引信息 ===")
        cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='index'")
        for idx in cursor.fetchall():
            print(f"\n索引名: {idx[0]}")
            print(f"创建语句: {idx[1]}")
            
        # 3. 检查示例数据
        print("\n=== 示例数据 ===")
        
        # stocks表示例数据
        cursor.execute("SELECT * FROM stocks LIMIT 5")
        print("\nstocks表前5条记录:")
        for row in cursor.fetchall():
            print(row)
            
        # stock_history表示例数据
        cursor.execute("SELECT * FROM stock_history LIMIT 5")
        print("\nstock_history表前5条记录:")
        for row in cursor.fetchall():
            print(row)
            
        # 4. 检查数据类型
        print("\n=== 数据类型检查 ===")
        cursor.execute("SELECT DISTINCT typeof(stock_code) FROM stocks")
        print("\nstocks表中stock_code的类型:", cursor.fetchall())
        
        cursor.execute("SELECT DISTINCT typeof(stock_code) FROM stock_history")
        print("stock_history表中stock_code的类型:", cursor.fetchall())
        
    finally:
        conn.close()

if __name__ == "__main__":
    check_db_structure() 