import sqlite3
import pandas as pd
from pathlib import Path
import os
import efinance as ef
from datetime import datetime, timedelta

def get_historical_data(stock_code, days=60):
    """
    获取股票的历史数据
    """
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
    
    try:
        df = ef.stock.get_quote_history(stock_code, beg=start_date, end=end_date)
        if df is not None and not df.empty:
            # 重命名列
            df = df.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount'
            })
            df['date'] = pd.to_datetime(df['date'])
            return df
    except Exception as e:
        print(f"获取股票 {stock_code} 历史数据时出错: {str(e)}")
    return None

def calculate_ma_indicators(df):
    """
    计算移动平均线指标
    """
    if df is None or df.empty:
        return None
        
    # 计算不同周期的移动平均线
    ma_periods = [5, 10, 20, 30, 60]
    for period in ma_periods:
        df[f'ma_{period}'] = df['close'].rolling(window=period).mean()
    
    # 只返回最新一天的数据
    return df.iloc[-1] if not df.empty else None

def add_ma_columns(conn):
    """
    在数据库中添加移动平均线字段
    """
    cursor = conn.cursor()
    
    # 添加MA列
    ma_columns = ['ma_5', 'ma_10', 'ma_20', 'ma_30', 'ma_60']
    for column in ma_columns:
        try:
            cursor.execute(f'''
            ALTER TABLE stocks ADD COLUMN {column} REAL DEFAULT NULL
            ''')
        except sqlite3.OperationalError as e:
            if "duplicate column name" not in str(e).lower():
                raise e
    conn.commit()

def update_stock_ma_data():
    """
    更新股票的移动平均线数据
    """
    try:
        # 连接数据库
        current_dir = Path(os.path.dirname(os.path.abspath(__file__)))
        db_path = current_dir / "stock_data.db"
        conn = sqlite3.connect(str(db_path))
        
        # 获取所有股票代码
        cursor = conn.cursor()
        cursor.execute("SELECT stock_code, stock_name FROM stocks")
        stocks = cursor.fetchall()
        
        # 添加MA列到数据库
        add_ma_columns(conn)
        
        # 更新每只股票的MA数据
        success_count = 0
        total_stocks = len(stocks)
        
        for i, (stock_code, stock_name) in enumerate(stocks, 1):
            print(f"\r处理进度: {i}/{total_stocks} - {stock_code} {stock_name}", end="")
            
            # 获取历史数据并计算MA
            hist_data = get_historical_data(stock_code)
            if hist_data is not None:
                latest_data = calculate_ma_indicators(hist_data)
                if latest_data is not None:
                    # 更新数据库
                    update_query = """
                    UPDATE stocks 
                    SET ma_5 = ?, ma_10 = ?, ma_20 = ?, ma_30 = ?, ma_60 = ?
                    WHERE stock_code = ?
                    """
                    cursor.execute(update_query, (
                        latest_data['ma_5'], latest_data['ma_10'], 
                        latest_data['ma_20'], latest_data['ma_30'], 
                        latest_data['ma_60'], stock_code
                    ))
                    success_count += 1
        
        conn.commit()
        print(f"\n\n成功更新 {success_count}/{total_stocks} 只股票的移动平均线数据")
        
        # 显示一些示例数据
        print("\n示例数据:")
        print("=" * 50)
        sample_query = """
        SELECT stock_code, stock_name, latest_price, ma_5, ma_10, ma_20, ma_30, ma_60
        FROM stocks
        WHERE ma_5 IS NOT NULL
        LIMIT 5
        """
        sample_df = pd.read_sql_query(sample_query, conn)
        print(sample_df)
        
        conn.close()
        
    except Exception as e:
        print(f"\n更新移动平均线数据时出错: {str(e)}")

if __name__ == "__main__":
    update_stock_ma_data() 