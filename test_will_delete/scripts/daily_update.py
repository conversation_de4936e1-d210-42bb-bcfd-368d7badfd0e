#!/usr/bin/env python3
"""
每日更新股票数据的脚本
每天早上6点运行，更新前一个交易日的数据
"""

import os
import sys
import time
import logging
import sqlite3
from datetime import datetime, timedelta
import baostock as bs
import pandas as pd
import schedule

def setup_logging():
    """设置日志"""
    log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # 设置日志格式
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # 设置文件日志
    log_file = os.path.join(log_dir, f'daily_update_{datetime.now().strftime("%Y%m%d")}.log')
    file_handler = logging.FileHandler(log_file)
    file_handler.setFormatter(formatter)
    
    # 设置控制台日志
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # 配置根日志记录器
    logging.root.setLevel(logging.INFO)
    logging.root.addHandler(file_handler)
    logging.root.addHandler(console_handler)

def get_last_trading_day():
    """获取最近的交易日"""
    bs_session = bs.login()
    try:
        today = datetime.now()
        # 查询从今天往前推7天的交易日历
        rs = bs_session.query_trade_dates(start_date=(today - timedelta(days=7)).strftime('%Y-%m-%d'),
                                        end_date=today.strftime('%Y-%m-%d'))
        trade_dates = rs.get_data()
        # 找到最近的交易日
        for _, row in trade_dates.iloc[::-1].iterrows():
            if row['is_trading_day'] == '1':
                return row['calendar_date']
    finally:
        bs_session.logout()
    return None

def update_stock_data(market):
    """更新指定市场的股票数据"""
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'stock.db')
    conn = sqlite3.connect(db_path)
    bs_session = bs.login()
    
    try:
        # 获取最近的交易日
        trade_date = get_last_trading_day()
        if not trade_date:
            logging.error("无法获取最近的交易日")
            return
            
        logging.info(f"开始更新 {market} 市场 {trade_date} 的数据")
        
        # 获取股票列表
        if market == 'sh':
            rs = bs_session.query_sh_a_stock_list()
            market_name = "上证A股"
        else:
            rs = bs_session.query_sz_a_stock_list()
            market_name = "深证A股"
            
        if rs.error_code != '0':
            logging.error(f"获取{market_name}列表失败: {rs.error_msg}")
            return
            
        stock_list = rs.get_data()
        total_stocks = len(stock_list)
        success_count = 0
        fail_count = 0
        
        for idx, row in stock_list.iterrows():
            try:
                # 获取日线数据
                rs = bs_session.query_history_k_data_plus(
                    code=row['code'],
                    fields="date,code,open,high,low,close,volume,amount",
                    start_date=trade_date,
                    end_date=trade_date,
                    frequency="d",
                    adjustflag="3"
                )
                
                if rs.error_code != '0':
                    logging.error(f"获取 {row['code']} 数据失败: {rs.error_msg}")
                    fail_count += 1
                    continue
                    
                data = rs.get_data()
                if not data.empty:
                    # 将数据写入数据库
                    data.to_sql('stock_history', conn, if_exists='append', index=False)
                    success_count += 1
                    
                # 打印进度
                if idx % 10 == 0:
                    logging.info(f"进度: {idx}/{total_stocks}")
                    
            except Exception as e:
                logging.error(f"处理 {row['code']} 时出错: {str(e)}")
                fail_count += 1
                
        logging.info(f"{market_name}更新完成 - 成功: {success_count}, 失败: {fail_count}")
        
    finally:
        bs_session.logout()
        conn.close()

def daily_task():
    """每日更新任务"""
    logging.info("开始执行每日更新任务")
    
    # 更新上证A股
    update_stock_data('sh')
    # 更新深证A股
    update_stock_data('sz')
    
    logging.info("每日更新任务完成")

def main():
    """主函数"""
    setup_logging()
    logging.info("启动每日更新服务")
    
    # 设置每天早上6点运行
    schedule.every().day.at("06:00").do(daily_task)
    
    # 立即运行一次
    daily_task()
    
    # 保持程序运行
    while True:
        schedule.run_pending()
        time.sleep(60)

if __name__ == "__main__":
    main() 