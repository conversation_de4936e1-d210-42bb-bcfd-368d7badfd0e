import sqlite3
from pathlib import Path

def check_stock(stock_code):
    """检查指定股票的数据"""
    db_path = Path("stock_data.db")
    conn = sqlite3.connect(str(db_path))
    
    try:
        cursor = conn.cursor()
        
        # 检查股票基本信息
        cursor.execute("""
        SELECT stock_code, stock_name 
        FROM stocks 
        WHERE stock_code = ?
        """, (stock_code,))
        stock_info = cursor.fetchone()
        
        print("\n=== 股票信息检查 ===")
        if stock_info:
            print(f"股票基本信息: {stock_info[0]} ({stock_info[1]})")
        else:
            print(f"在stocks表中未找到股票 {stock_code}")
        
        # 检查历史数据
        cursor.execute("""
        SELECT date, open, high, low, close, volume, amount
        FROM stock_history
        WHERE stock_code = ?
        ORDER BY date
        """, (stock_code,))
        history_data = cursor.fetchall()
        
        print("\n历史数据:")
        if history_data:
            print(f"共有 {len(history_data)} 条记录")
            print("\n前5条记录:")
            for i, record in enumerate(history_data[:5]):
                print(f"{i+1}. 日期: {record[0]}, 开盘: {record[1]}, 收盘: {record[4]}, 成交量: {record[5]}")
        else:
            print(f"在stock_history表中未找到股票 {stock_code} 的数据")
            
        # 检查是否存在其他格式的代码
        cursor.execute("""
        SELECT DISTINCT stock_code 
        FROM stock_history 
        WHERE stock_code LIKE ?
        """, (f"%{stock_code}%",))
        similar_codes = cursor.fetchall()
        
        if similar_codes:
            print("\n发现类似的股票代码:")
            for code in similar_codes:
                print(f"- {code[0]}")
            
    finally:
        conn.close()

if __name__ == "__main__":
    check_stock('000001')  # 检查平安银行的数据 