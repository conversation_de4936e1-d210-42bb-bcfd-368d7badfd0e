#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
实时选股脚本，功能：
1. 下载实时数据
2. 根据SQL条件粗选股票
3. 将选中的股票数据写入stock_history表


执行顺序：
1、执行实时股票下载（开盘时间，粗选）
python3 realtime_stock_picker.py --mode select
2、计算技术指标
python3 scripts/update_technical_indicators.py --start_date 20250604 --end_date 20250604
3、执行选股
python3 run_select_strategy.py查看实时选股结果

"""

import os
import sys
import argparse
import pandas as pd
from datetime import datetime
import efinance as ef
from typing import Dict, List, Optional
import logging

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from strategies.utils.db_utils import DBUtils

# 设置日志
def setup_logger():
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 设置文件日志
    log_file = os.path.join(log_dir, f"stock_picker_{datetime.now().strftime('%Y%m%d')}.log")
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # 设置控制台日志
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 设置日志格式
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 配置根日志记录器
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    logger.handlers.clear()
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

class RealtimeStockPicker:
    def __init__(self):
        self.db = DBUtils()
        self.realtime_data_file = "stock_list_full.csv"
        self.logger = setup_logger()
        self.incomplete_data_count = 0
        self.error_count = 0

    def get_prefiltered_stocks(self, realtime_data: pd.DataFrame) -> pd.DataFrame:
        """
        从实时数据中筛选符合粗排条件的股票
        条件包括：
        1. 成交额 > 3亿
        2. 5元 < 价格 < 200元
        3. 非ST股票
        4. 上市时间 > 3个月
        5. 换手率 > 5%
        6. 当前股价比最低价高1%以上
        7. 冲高回落小于4%
        """
        try:
            # 确保数值类型正确
            realtime_data['amount'] = pd.to_numeric(realtime_data['amount'], errors='coerce')
            realtime_data['current_price'] = pd.to_numeric(realtime_data['current_price'], errors='coerce')
            realtime_data['turnover_rate'] = pd.to_numeric(realtime_data['turnover_rate'], errors='coerce')
            realtime_data['high'] = pd.to_numeric(realtime_data['high'], errors='coerce')
            realtime_data['low'] = pd.to_numeric(realtime_data['low'], errors='coerce')
            
            # 获取上市超过3个月的股票列表
            conn = self.db.get_connection()
            mature_stocks = pd.read_sql("""
                SELECT DISTINCT stock_code 
                FROM stock_history 
                WHERE date <= CURRENT_DATE 
                GROUP BY stock_code 
                HAVING COUNT(*) > 60
            """, conn)
            
            # 计算冲高回落比例
            realtime_data['high_fallback'] = (realtime_data['high'] - realtime_data['current_price']) / realtime_data['current_price']
            # 计算当前价格相对最低价的涨幅
            realtime_data['price_vs_low'] = realtime_data['current_price'] / realtime_data['low']
            
            # 应用筛选条件
            filtered_stocks = realtime_data[
                (realtime_data['amount'] > 300000000) &  # 成交额大于3亿
                (realtime_data['current_price'].between(5, 200)) &  # 价格在5-200之间
                (~realtime_data['stock_name'].str.contains('ST', na=False)) &  # 非ST股票
                (realtime_data['stock_code'].isin(mature_stocks['stock_code'])) &  # 上市超过3个月
                (realtime_data['turnover_rate'] > 5) &  # 换手率大于5%
                (realtime_data['price_vs_low'] >= 1.01) &  # 当前价格比最低价高1%以上
                (realtime_data['high_fallback'] < 0.04)  # 冲高回落小于4%
            ].copy()
            
            # 按成交额排序并只保留前10条记录
            filtered_stocks = filtered_stocks.sort_values('amount', ascending=False).head(1000)
            
            if not filtered_stocks.empty:
                print(f"\n初筛结果统计：")
                print(f"符合条件的股票数量: {len(filtered_stocks)}")
                print(f"成交额范围: {filtered_stocks['amount'].min()/1e8:.2f}亿 - {filtered_stocks['amount'].max()/1e8:.2f}亿")
                print(f"价格范围: {filtered_stocks['current_price'].min():.2f} - {filtered_stocks['current_price'].max():.2f}")
                print(f"换手率范围: {filtered_stocks['turnover_rate'].min():.2f}% - {filtered_stocks['turnover_rate'].max():.2f}%")
                print(f"冲高回落范围: {filtered_stocks['high_fallback'].min()*100:.2f}% - {filtered_stocks['high_fallback'].max()*100:.2f}%")
                print(f"相对最低价涨幅范围: {(filtered_stocks['price_vs_low'].min()-1)*100:.2f}% - {(filtered_stocks['price_vs_low'].max()-1)*100:.2f}%")
                
                # 打印具体的股票信息
                print("\n选出的股票详情：")
                print("代码      名称     现价    成交额(亿)  换手率")
                print("-" * 50)
                for _, row in filtered_stocks.iterrows():
                    print(f"{row['stock_code']:<9} {row['stock_name']:<8} {row['current_price']:6.2f} {row['amount']/1e8:8.2f} {row['turnover_rate']:8.2f}%")
            
            return filtered_stocks
            
        except Exception as e:
            self.logger.error(f"获取初筛股票时出错: {str(e)}")
            return pd.DataFrame()

    def get_realtime_data(self, force_online: bool = False) -> pd.DataFrame:
        """
        获取实时行情数据
        Args:
            force_online: 是否强制从在线获取数据
        """
        # 如果不是强制在线获取，先尝试读取本地文件
        if not force_online and os.path.exists(self.realtime_data_file):
            try:
                df = pd.read_csv(self.realtime_data_file)
                if not df.empty:
                    return df
            except Exception as e:
                print(f"读取本地数据文件失败: {str(e)}")
        
        # 本地文件不存在或强制在线获取时，从网络获取
        try:
            # 使用efinance获取A股实时行情
            stock_df = ef.stock.get_realtime_quotes()
            
            # 保存原始列名映射，用于显示所有字段
            print("\n获取到以下字段：")
            for col in stock_df.columns:
                print(f"- {col}")
            
            # 重命名列以匹配系统格式
            column_mapping = {
                '股票代码': 'stock_code',
                '股票名称': 'stock_name',
                '最新价': 'current_price',
                '涨跌幅': 'change_percent',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '最高': 'high',
                '最低': 'low',
                '今开': 'open',
                '昨收': 'pre_close',
                '换手率': 'turnover_rate',
                '市盈率-动态': 'pe_ratio',
                '市净率': 'pb_ratio',
                '总市值': 'market_cap',
                '流通市值': 'circulating_market_cap',
                '涨跌额': 'change_amount',
                '内盘': 'inner_vol',
                '外盘': 'outer_vol'
            }
            
            # 创建新的DataFrame，保留所有原始列
            result_df = stock_df.copy()
            
            # 重命名必要的列
            for old_name, new_name in column_mapping.items():
                if old_name in result_df.columns:
                    result_df[new_name] = result_df[old_name]
            
            # 添加市场标识
            result_df['market'] = result_df['stock_code'].apply(
                lambda x: 'sh' if str(x).startswith('6') else 'sz'
            )
            
            # 保存完整数据
            result_df.to_csv(self.realtime_data_file, index=False, encoding='utf-8')
            print(f"\n数据已保存到 {self.realtime_data_file}")
            print(f"总行数: {len(result_df)}, 总字段数: {len(result_df.columns)}")
            
            return result_df
            
        except Exception as e:
            print(f"获取在线数据失败: {str(e)}")
            return pd.DataFrame()

    def convert_to_float(self, value) -> Optional[float]:
        """将字符串转换为float，处理特殊字符"""
        if pd.isna(value) or value == '-' or value == '':
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None

    def save_to_stock_history(self, realtime_data: pd.DataFrame):
        """将实时数据保存到stock_history表"""
        current_date = datetime.now().strftime('%Y-%m-%d')
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            conn.execute("BEGIN TRANSACTION")
            updated_stocks = []
            self.incomplete_data_count = 0
            
            for _, row in realtime_data.iterrows():
                data = {
                    'stock_code': row['stock_code'],
                    'stock_name': row['stock_name'],
                    'date': current_date,
                    'open': self.convert_to_float(row['open']),
                    'high': self.convert_to_float(row['high']),
                    'low': self.convert_to_float(row['low']),
                    'close': self.convert_to_float(row['current_price']),
                    'volume': self.convert_to_float(row['volume']),
                    'amount': self.convert_to_float(row['amount']),
                    'amplitude': self.convert_to_float(row['amplitude']) if 'amplitude' in row else None,
                    'change_percent': self.convert_to_float(row['change_percent']) if 'change_percent' in row else None,
                    'change_amount': self.convert_to_float(row['change_amount']) if 'change_amount' in row else None,
                    'turnover_rate': self.convert_to_float(row['turnover_rate']) if 'turnover_rate' in row else None,
                    'total_value': self.convert_to_float(row['market_cap']) if 'market_cap' in row else None,
                    'float_value': self.convert_to_float(row['circulating_market_cap']) if 'circulating_market_cap' in row else None
                }
                
                # 检查必要字段
                required_fields = ['open', 'high', 'low', 'close', 'volume', 'amount']
                if any(data[field] is None for field in required_fields):
                    self.incomplete_data_count += 1
                    self.logger.debug(f"股票 {row['stock_code']} 数据不完整，跳过更新")
                    continue
                
                valid_data = {k: v for k, v in data.items() if v is not None}
                fields = ', '.join(valid_data.keys())
                placeholders = ', '.join(['?' for _ in valid_data])
                values = tuple(valid_data.values())
                
                sql = f"""
                    REPLACE INTO stock_history ({fields})
                    VALUES ({placeholders})
                """
                
                try:
                    cursor.execute(sql, values)
                    updated_stocks.append(row['stock_code'])
                except Exception as e:
                    self.logger.error(f"更新股票 {row['stock_code']} 数据时出错: {str(e)}")
                    continue
            
            conn.commit()
            print(f"\n成功保存 {len(updated_stocks)} 只股票数据到stock_history表")
            if self.incomplete_data_count > 0:
                print(f"{self.incomplete_data_count} 只股票数据不完整，已跳过更新")
            
        except Exception as e:
            conn.rollback()
            self.logger.error(f"保存数据到stock_history表时出错: {str(e)}")
        finally:
            cursor.close()

    def run_realtime_selection(self):
        """运行实时选股"""
        # 1. 获取实时数据
        print("\n1. 获取实时数据...")
        realtime_data = self.get_realtime_data(force_online=True)
        if realtime_data.empty:
            print("未获取到实时数据，请检查网络连接")
            return
        
        # 2. 粗排
        print("\n2. 执行股票初筛...")
        filtered_stocks = self.get_prefiltered_stocks(realtime_data)
        if filtered_stocks.empty:
            print("未找到符合初筛条件的股票")
            return
        
        # 3. 保存到 stock_history
        print("\n3. 保存筛选股票数据...")
        self.save_to_stock_history(filtered_stocks)

def main():
    parser = argparse.ArgumentParser(description='实时选股系统')
    parser.add_argument('--mode', choices=['update', 'select'], required=True,
                      help='运行模式：update-强制更新在线数据，select-运行选股')
    args = parser.parse_args()
    
    picker = RealtimeStockPicker()
    
    if args.mode == 'update':
        print(f"\n{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 强制更新在线数据...")
        realtime_data = picker.get_realtime_data(force_online=True)
        if not realtime_data.empty:
            picker.save_to_stock_history(realtime_data)
            print("数据更新成功")
        else:
            print("数据更新失败")
    else:
        picker.run_realtime_selection()

if __name__ == "__main__":
    main() 