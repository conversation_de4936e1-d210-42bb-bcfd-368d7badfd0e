#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票选股系统快速启动脚本
提供交互式菜单，方便用户快速使用各种功能
"""

import os
import sys
import subprocess
from datetime import datetime, timedelta

def print_banner():
    """打印系统横幅"""
    print("=" * 60)
    print("🚀 股票选股策略系统")
    print("=" * 60)
    print()

def print_menu():
    """打印主菜单"""
    print("请选择要执行的功能：")
    print()
    print("📊 数据管理")
    print("  1. 下载历史数据（沪市）")
    print("  2. 下载历史数据（深市）")
    print("  3. 更新实时数据")
    print("  4. 计算技术指标")
    print()
    print("🎯 选股功能")
    print("  5. 实时选股（完整流程）")
    print("  6. 执行选股策略")
    print()
    print("📈 交易回测")
    print("  7. 运行交易策略回测")
    print()
    print("🔧 系统工具")
    print("  8. 初始化数据库")
    print("  9. 查看数据库状态")
    print("  0. 退出系统")
    print()

def get_date_range():
    """获取日期范围"""
    print("请输入日期范围（格式：YYYYMMDD）")
    
    # 默认日期：最近5个交易日
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    
    default_start = start_date.strftime('%Y%m%d')
    default_end = end_date.strftime('%Y%m%d')
    
    start_input = input(f"开始日期 (默认: {default_start}): ").strip()
    if not start_input:
        start_input = default_start
        
    end_input = input(f"结束日期 (默认: {default_end}): ").strip()
    if not end_input:
        end_input = default_end
    
    return start_input, end_input

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n🔄 {description}...")
    print(f"执行命令: {command}")
    print("-" * 50)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.stdout:
            print(result.stdout)
        
        if result.stderr:
            print("错误信息:", result.stderr)
            
        if result.returncode == 0:
            print(f"✅ {description}完成")
        else:
            print(f"❌ {description}失败，返回码: {result.returncode}")
            
    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")
    
    print("-" * 50)
    input("按回车键继续...")

def check_database_status():
    """检查数据库状态"""
    print("\n📊 数据库状态检查...")
    
    commands = [
        ("SELECT COUNT(*) as total_records FROM stock_history", "总记录数"),
        ("SELECT COUNT(DISTINCT stock_code) as total_stocks FROM stock_history", "股票数量"),
        ("SELECT MIN(date) as min_date, MAX(date) as max_date FROM stock_history", "数据日期范围"),
        ("SELECT COUNT(*) as records_with_ma5 FROM stock_history WHERE ma5 IS NOT NULL", "有技术指标的记录数")
    ]
    
    for sql, desc in commands:
        cmd = f'sqlite3 stock_data.db "{sql}"'
        print(f"\n{desc}:")
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"  {result.stdout.strip()}")
            else:
                print(f"  查询失败: {result.stderr}")
        except Exception as e:
            print(f"  查询异常: {str(e)}")
    
    input("\n按回车键继续...")

def main():
    """主函数"""
    while True:
        os.system('clear' if os.name == 'posix' else 'cls')  # 清屏
        print_banner()
        print_menu()
        
        choice = input("请输入选项 (0-9): ").strip()
        
        if choice == '0':
            print("\n👋 感谢使用股票选股系统！")
            break
            
        elif choice == '1':
            start_date, end_date = get_date_range()
            cmd = f"python3 scripts/download_efinance_history.py --market sh --start_date {start_date} --end_date {end_date}"
            run_command(cmd, "下载沪市历史数据")
            
        elif choice == '2':
            start_date, end_date = get_date_range()
            cmd = f"python3 scripts/download_efinance_history.py --market sz --start_date {start_date} --end_date {end_date}"
            run_command(cmd, "下载深市历史数据")
            
        elif choice == '3':
            cmd = "python3 realtime_stock_picker.py --mode update"
            run_command(cmd, "更新实时数据")
            
        elif choice == '4':
            start_date, end_date = get_date_range()
            cmd = f"python3 scripts/update_technical_indicators.py --start_date {start_date} --end_date {end_date}"
            run_command(cmd, "计算技术指标")
            
        elif choice == '5':
            print("\n🎯 执行完整实时选股流程...")
            
            # 步骤1：获取实时数据
            cmd1 = "python3 realtime_stock_picker.py --mode select"
            run_command(cmd1, "获取实时数据并粗选")
            
            # 步骤2：计算技术指标
            today = datetime.now().strftime('%Y%m%d')
            cmd2 = f"python3 scripts/update_technical_indicators.py --start_date {today} --end_date {today}"
            run_command(cmd2, "计算当日技术指标")
            
            # 步骤3：执行选股策略
            cmd3 = "python3 run_select_strategy.py"
            run_command(cmd3, "执行选股策略")
            
        elif choice == '6':
            cmd = "python3 run_select_strategy.py"
            run_command(cmd, "执行选股策略")
            
        elif choice == '7':
            cmd = "python3 run_stock_strategy.py"
            run_command(cmd, "运行交易策略回测")
            
        elif choice == '8':
            cmd = "python3 scripts/init_db.py"
            run_command(cmd, "初始化数据库")
            
        elif choice == '9':
            check_database_status()
            
        else:
            print("\n❌ 无效选项，请重新选择")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
