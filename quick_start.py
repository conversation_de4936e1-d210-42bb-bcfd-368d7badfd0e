#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票选股系统快速启动脚本
提供交互式菜单和自动执行功能，方便用户快速使用各种功能
"""

import os
import sys
import subprocess
import time
import warnings
from datetime import datetime, timedelta

# 忽略SSL警告
warnings.filterwarnings("ignore", message="urllib3 v2 only supports OpenSSL 1.1.1+")
os.environ['PYTHONWARNINGS'] = 'ignore::urllib3.exceptions.NotOpenSSLWarning'

def print_banner():
    """打印系统横幅"""
    print("=" * 60)
    print("🚀 股票选股策略系统")
    print("=" * 60)
    print()

def print_menu():
    """打印主菜单"""
    print("请选择要执行的功能：")
    print()
    print("📊 数据管理")
    print("  1. 下载历史数据（沪市）")
    print("  2. 下载历史数据（深市）")
    print("  3. 更新实时数据")
    print("  4. 计算技术指标")
    print()
    print("🎯 选股功能")
    print("  5. 实时选股（完整流程）")
    print("  6. 执行选股策略")
    print()
    print("📈 交易回测")
    print("  7. 运行交易策略回测")
    print()
    print("🔧 系统工具")
    print("  8. 初始化数据库")
    print("  9. 查看数据库状态")
    print()
    print("🚀 自动执行")
    print("  A. 自动执行完整流程（推荐）")
    print("  B. 自动更新今日数据")
    print()
    print("  0. 退出系统")
    print()

def get_date_range():
    """获取日期范围"""
    print("请输入日期范围（格式：YYYYMMDD）")
    
    # 默认日期：最近5个交易日
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    
    default_start = start_date.strftime('%Y%m%d')
    default_end = end_date.strftime('%Y%m%d')
    
    start_input = input(f"开始日期 (默认: {default_start}): ").strip()
    if not start_input:
        start_input = default_start
        
    end_input = input(f"结束日期 (默认: {default_end}): ").strip()
    if not end_input:
        end_input = default_end
    
    return start_input, end_input

def run_command(command, description, auto_mode=False):
    """运行命令并显示结果"""
    print(f"\n🔄 {description}...")
    print(f"执行命令: {command}")
    print("-" * 50)

    success = False
    try:
        # 设置环境变量来忽略警告
        env = os.environ.copy()
        env['PYTHONWARNINGS'] = 'ignore::urllib3.exceptions.NotOpenSSLWarning'

        result = subprocess.run(command, shell=True, capture_output=True, text=True, env=env)

        if result.stdout:
            # 过滤掉SSL警告
            output_lines = result.stdout.split('\n')
            filtered_lines = [line for line in output_lines if 'NotOpenSSLWarning' not in line and 'urllib3' not in line]
            if filtered_lines:
                print('\n'.join(filtered_lines))

        if result.stderr:
            # 过滤掉SSL警告
            error_lines = result.stderr.split('\n')
            filtered_errors = [line for line in error_lines if 'NotOpenSSLWarning' not in line and 'urllib3' not in line]
            if filtered_errors and any(line.strip() for line in filtered_errors):
                print("错误信息:", '\n'.join(filtered_errors))

        if result.returncode == 0:
            print(f"✅ {description}完成")
            success = True
        else:
            print(f"❌ {description}失败，返回码: {result.returncode}")

    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")

    print("-" * 50)

    if not auto_mode:
        input("按回车键继续...")
    else:
        time.sleep(2)  # 自动模式下等待2秒

    return success

def check_database_status():
    """检查数据库状态"""
    print("\n📊 数据库状态检查...")
    
    commands = [
        ("SELECT COUNT(*) as total_records FROM stock_history", "总记录数"),
        ("SELECT COUNT(DISTINCT stock_code) as total_stocks FROM stock_history", "股票数量"),
        ("SELECT MIN(date) as min_date, MAX(date) as max_date FROM stock_history", "数据日期范围"),
        ("SELECT COUNT(*) as records_with_ma5 FROM stock_history WHERE ma5 IS NOT NULL", "有技术指标的记录数")
    ]
    
    for sql, desc in commands:
        cmd = f'sqlite3 stock_data.db "{sql}"'
        print(f"\n{desc}:")
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"  {result.stdout.strip()}")
            else:
                print(f"  查询失败: {result.stderr}")
        except Exception as e:
            print(f"  查询异常: {str(e)}")
    
    input("\n按回车键继续...")

def auto_complete_workflow():
    """自动执行完整工作流程"""
    print("\n🚀 开始自动执行完整工作流程...")
    print("=" * 60)

    # 获取日期范围（最近5天）
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    start_str = start_date.strftime('%Y%m%d')
    end_str = end_date.strftime('%Y%m%d')
    today_str = end_date.strftime('%Y%m%d')

    print(f"📅 数据日期范围: {start_str} - {end_str}")
    print(f"📅 今日日期: {today_str}")

    steps = [
        {
            "name": "下载沪市历史数据",
            "command": f"python3 scripts/download_efinance_history.py --market sh --start_date {start_str} --end_date {end_str}",
            "critical": False
        },
        {
            "name": "下载深市历史数据",
            "command": f"python3 scripts/download_efinance_history.py --market sz --start_date {start_str} --end_date {end_str}",
            "critical": False
        },
        {
            "name": "更新实时数据",
            "command": "python3 realtime_stock_picker.py --mode update",
            "critical": True
        },
        {
            "name": "计算技术指标",
            "command": f"python3 scripts/update_technical_indicators.py --start_date {today_str} --end_date {today_str}",
            "critical": True
        },
        {
            "name": "执行选股策略",
            "command": "python3 run_select_strategy.py",
            "critical": True
        }
    ]

    success_count = 0
    total_steps = len(steps)

    for i, step in enumerate(steps, 1):
        print(f"\n📍 步骤 {i}/{total_steps}: {step['name']}")
        success = run_command(step['command'], step['name'], auto_mode=True)

        if success:
            success_count += 1
        elif step['critical']:
            print(f"⚠️ 关键步骤失败，是否继续？")
            choice = input("输入 'y' 继续，其他键退出: ").strip().lower()
            if choice != 'y':
                break

    print(f"\n🎯 工作流程完成！")
    print(f"✅ 成功执行: {success_count}/{total_steps} 个步骤")

    if success_count == total_steps:
        print("🎉 所有步骤都成功完成！")
    elif success_count >= 3:
        print("✨ 主要功能已完成，可以查看选股结果")
    else:
        print("⚠️ 部分步骤失败，请检查错误信息")

    input("\n按回车键返回主菜单...")

def auto_update_today():
    """自动更新今日数据"""
    print("\n🔄 开始自动更新今日数据...")
    print("=" * 60)

    today_str = datetime.now().strftime('%Y%m%d')

    steps = [
        {
            "name": "获取实时数据并粗选",
            "command": "python3 realtime_stock_picker.py --mode select",
            "critical": True
        },
        {
            "name": "计算今日技术指标（优化版）",
            "command": f"python3 scripts/update_technical_indicators.py --start_date {today_str} --end_date {today_str}",
            "critical": True
        },
        {
            "name": "执行选股策略",
            "command": "python3 run_select_strategy.py",
            "critical": True
        }
    ]

    success_count = 0
    for i, step in enumerate(steps, 1):
        print(f"\n📍 步骤 {i}/{len(steps)}: {step['name']}")
        success = run_command(step['command'], step['name'], auto_mode=True)
        if success:
            success_count += 1
        elif step.get('critical', False):
            print(f"⚠️ 关键步骤失败，是否继续？")
            choice = input("输入 'y' 继续，其他键退出: ").strip().lower()
            if choice != 'y':
                break

    print(f"\n🎯 今日数据更新完成！")
    print(f"✅ 成功执行: {success_count}/{len(steps)} 个步骤")

    if success_count == len(steps):
        print("🎉 所有步骤都成功完成！")
    elif success_count >= 2:
        print("✨ 主要功能已完成，可以查看选股结果")
    else:
        print("⚠️ 部分步骤失败，请检查错误信息")

    input("\n按回车键返回主菜单...")

def main():
    """主函数"""
    while True:
        os.system('clear' if os.name == 'posix' else 'cls')  # 清屏
        print_banner()
        print_menu()
        
        choice = input("请输入选项 (0-9, A, B): ").strip().upper()

        if choice == '0':
            print("\n👋 感谢使用股票选股系统！")
            break

        elif choice == 'A':
            auto_complete_workflow()

        elif choice == 'B':
            auto_update_today()
            
        elif choice == '1':
            start_date, end_date = get_date_range()
            cmd = f"python3 scripts/download_efinance_history.py --market sh --start_date {start_date} --end_date {end_date}"
            run_command(cmd, "下载沪市历史数据")
            
        elif choice == '2':
            start_date, end_date = get_date_range()
            cmd = f"python3 scripts/download_efinance_history.py --market sz --start_date {start_date} --end_date {end_date}"
            run_command(cmd, "下载深市历史数据")
            
        elif choice == '3':
            cmd = "python3 realtime_stock_picker.py --mode update"
            run_command(cmd, "更新实时数据")
            
        elif choice == '4':
            start_date, end_date = get_date_range()
            cmd = f"python3 scripts/update_technical_indicators.py --start_date {start_date} --end_date {end_date}"
            run_command(cmd, "计算技术指标")
            
        elif choice == '5':
            print("\n🎯 执行完整实时选股流程...")
            
            # 步骤1：获取实时数据
            cmd1 = "python3 realtime_stock_picker.py --mode select"
            run_command(cmd1, "获取实时数据并粗选")
            
            # 步骤2：计算技术指标
            today = datetime.now().strftime('%Y%m%d')
            cmd2 = f"python3 scripts/update_technical_indicators.py --start_date {today} --end_date {today}"
            run_command(cmd2, "计算当日技术指标")
            
            # 步骤3：执行选股策略
            cmd3 = "python3 run_select_strategy.py"
            run_command(cmd3, "执行选股策略")
            
        elif choice == '6':
            cmd = "python3 run_select_strategy.py"
            run_command(cmd, "执行选股策略")
            
        elif choice == '7':
            cmd = "python3 run_stock_strategy.py"
            run_command(cmd, "运行交易策略回测")
            
        elif choice == '8':
            cmd = "python3 scripts/init_db.py"
            run_command(cmd, "初始化数据库")
            
        elif choice == '9':
            check_database_status()
            
        else:
            print("\n❌ 无效选项，请重新选择")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
