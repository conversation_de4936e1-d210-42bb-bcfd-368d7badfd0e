# 股票数据库字段说明文档

## 数据库表结构

### 1. stocks（股票基本信息表）
| 字段名 | 类型 | 说明 | 主键 |
|-------|------|------|------|
| code | TEXT | 股票代码 | 是 |
| name | TEXT | 股票名称 | 否 |
| market | TEXT | 交易市场 | 否 |

### 2. stock_history（股票历史数据表）
#### 2.1 基础交易数据
| 字段名 | 类型 | 说明 | 计算方式 |
|-------|------|------|----------|
| stock_code | TEXT | 股票代码 | - |
| stock_name | TEXT | 股票名称 | - |
| date | TEXT | 交易日期 | - |
| open | REAL | 开盘价 | - |
| high | REAL | 最高价 | - |
| low | REAL | 最低价 | - |
| close | REAL | 收盘价 | - |
| volume | REAL | 成交量 | - |
| amount | REAL | 成交额 | - |
| amplitude | REAL | 振幅 | (最高价 - 最低价) / 前收盘价 × 100% |
| change_percent | REAL | 涨跌幅 | (收盘价 - 前收盘价) / 前收盘价 × 100% |
| change_amount | REAL | 涨跌额 | 收盘价 - 前收盘价 |
| turnover_rate | REAL | 换手率 | 成交量 / 流通股本 × 100% |

#### 2.2 基本面指标
| 字段名 | 类型 | 说明 | 计算方式 |
|-------|------|------|----------|
| net_profit | REAL | 净利润 | - |
| total_value | REAL | 总市值 | 总股本 × 收盘价 |
| float_value | REAL | 流通市值 | 流通股本 × 收盘价 |
| industry | TEXT | 所处行业 | - |
| pe_ratio | REAL | 市盈率(动态) | 总市值 / 净利润 |
| pb_ratio | REAL | 市净率 | 总市值 / 净资产 |
| roe | REAL | 净资产收益率 | 净利润 / 净资产 × 100% |
| gross_profit_margin | REAL | 毛利率 | (营业收入 - 营业成本) / 营业收入 × 100% |
| net_profit_margin | REAL | 净利率 | 净利润 / 营业收入 × 100% |
| sector_code | TEXT | 板块编号 | - |

#### 2.3 均线指标
| 字段名 | 类型 | 说明 | 计算方式 |
|-------|------|------|----------|
| ma5 | FLOAT | 5日均线 | 5日收盘价的算术平均值 |
| ma10 | FLOAT | 10日均线 | 10日收盘价的算术平均值 |
| ma20 | FLOAT | 20日均线 | 20日收盘价的算术平均值 |
| ma30 | FLOAT | 30日均线 | 30日收盘价的算术平均值 |
| ma60 | FLOAT | 60日均线 | 60日收盘价的算术平均值 |

#### 2.4 成交量指标
| 字段名 | 类型 | 说明 | 计算方式 |
|-------|------|------|----------|
| volume_ma5 | FLOAT | 5日成交量均线 | 5日成交量的算术平均值 |
| volume_ma10 | FLOAT | 10日成交量均线 | 10日成交量的算术平均值 |
| volume_ma20 | FLOAT | 20日成交量均线 | 20日成交量的算术平均值 |
| volume_ratio_5d | FLOAT | 5日量比 | 当日成交量 / 5日平均成交量 |
| volume_ratio_10d | FLOAT | 10日量比 | 当日成交量 / 10日平均成交量 |

#### 2.5 波动指标
| 字段名 | 类型 | 说明 | 计算方式 |
|-------|------|------|----------|
| amplitude_5d | FLOAT | 5日振幅 | 5日内(最高价 - 最低价) / 5日前收盘价 × 100% |
| amplitude_10d | FLOAT | 10日振幅 | 10日内(最高价 - 最低价) / 10日前收盘价 × 100% |
| daily_volatility | FLOAT | 日内波动率 | 日内最高价与最低价的波动范围 |

#### 2.6 均线偏离度
| 字段名 | 类型 | 说明 | 计算方式 |
|-------|------|------|----------|
| ma5_deviation | FLOAT | 5日均线偏离度 | (收盘价 - MA5) / MA5 × 100% |
| ma10_deviation | FLOAT | 10日均线偏离度 | (收盘价 - MA10) / MA10 × 100% |
| ma_trend | FLOAT | 均线趋势 | 基于多条均线的综合趋势判断 |

#### 2.7 支撑压力指标
| 字段名 | 类型 | 说明 | 计算方式 |
|-------|------|------|----------|
| pressure_ma5 | FLOAT | 5日均线压力 | 收盘价高于MA5时的压力指数 |
| pressure_ma10 | FLOAT | 10日均线压力 | 收盘价高于MA10时的压力指数 |
| pressure_ma20 | FLOAT | 20日均线压力 | 收盘价高于MA20时的压力指数 |
| support_ma5 | FLOAT | 5日均线支撑 | 收盘价低于MA5时的支撑指数 |
| support_ma10 | FLOAT | 10日均线支撑 | 收盘价低于MA10时的支撑指数 |
| support_ma20 | FLOAT | 20日均线支撑 | 收盘价低于MA20时的支撑指数 |

#### 2.8 量价关系指标
| 字段名 | 类型 | 说明 | 计算方式 |
|-------|------|------|----------|
| volume_price_pressure_5d | FLOAT | 5日量价压力 | 基于5日内高于当前价格的成交量加权计算 |
| volume_price_pressure_10d | FLOAT | 10日量价压力 | 基于10日内高于当前价格的成交量加权计算 |
| volume_price_support_5d | FLOAT | 5日量价支撑 | 基于5日内低于当前价格的成交量加权计算 |
| volume_price_support_10d | FLOAT | 10日量价支撑 | 基于10日内低于当前价格的成交量加权计算 |

#### 2.9 成交量分布
| 字段名 | 类型 | 说明 | 计算方式 |
|-------|------|------|----------|
| volume_distribution_high | FLOAT | 高位成交量占比 | 高价区间成交量 / 总成交量 |
| volume_distribution_mid | FLOAT | 中位成交量占比 | 中价区间成交量 / 总成交量 |
| volume_distribution_low | FLOAT | 低位成交量占比 | 低价区间成交量 / 总成交量 |

#### 2.10 突破回踩指标
| 字段名 | 类型 | 说明 | 计算方式 |
|-------|------|------|----------|
| breakthrough_ma5 | FLOAT | 突破5日均线 | 向上突破MA5的强度指标 |
| breakthrough_ma10 | FLOAT | 突破10日均线 | 向上突破MA10的强度指标 |
| pullback_ma5 | FLOAT | 回踩5日均线 | 回踩MA5的支撑强度指标 |
| pullback_ma10 | FLOAT | 回踩10日均线 | 回踩MA10的支撑强度指标 |

## 数据库索引说明
1. `idx_stock_history_date`: 按日期检索的索引
2. `idx_stock_history_code_date`: 按股票代码和日期联合检索的索引
3. `idx_stock_history_date_code`: 按日期和股票代码联合检索的索引
4. `idx_stocks_code`: 股票代码索引
5. `idx_stock_history_ma5`: 5日均线索引
6. `idx_stock_history_ma10`: 10日均线索引
7. `idx_stock_history_amplitude_5d`: 5日振幅索引
8. `idx_stock_history_volume_ratio_5d`: 5日量比索引
9. `idx_stock_history_pressure_ma5`: 5日均线压力索引
10. `idx_stock_history_support_ma5`: 5日均线支撑索引 