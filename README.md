# 股票选股策略系统

这是一个基于技术指标的股票选股和交易策略系统，支持实时数据获取、技术指标计算、选股策略执行和回测功能。

## 📁 项目结构

```
├── README.md                    # 项目说明文档
├── requirements.txt             # Python依赖包
├── stock_data.db               # 核心数据库文件
├── stock_data_backup_*.db      # 数据库备份文件
├── stock_list_full.csv         # 股票列表文件
│
├── 📁 scripts/                 # 数据处理脚本
│   ├── download_efinance_history.py    # 历史数据下载
│   ├── update_technical_indicators.py  # 技术指标计算
│   ├── init_db.py                     # 数据库初始化
│   ├── add_technical_indicators.sql   # 技术指标字段SQL
│   ├── create_trades_table.sql        # 交易表创建SQL
│   └── stock_list_full.csv           # 股票列表数据
│
├── 📁 strategies/              # 策略模块
│   ├── stock_picker/          # 选股策略
│   │   ├── strategies/        # 具体选股策略实现
│   │   ├── base.py           # 选股策略基类
│   │   ├── run_strategy.py   # 策略运行脚本
│   │   └── batch_stock_selection.py  # 批量选股
│   ├── trader/               # 交易策略
│   ├── backtest/             # 回测模块
│   └── utils/                # 工具模块
│       └── db_utils.py       # 数据库工具类
│
├── 📁 logs/                   # 日志文件
├── 📁 daily_data/             # 日线数据文件
│
├── run_select_strategy.py     # 🚀 核心入口：选股策略运行
├── realtime_stock_picker.py  # 🚀 核心入口：实时选股
└── run_stock_strategy.py     # 🚀 核心入口：交易策略回测
```

## 🚀 核心功能

### 1. 实时数据获取
- 支持从efinance获取A股实时行情数据
- 自动保存到本地数据库
- 支持增量更新和全量更新

### 2. 技术指标计算
- 移动平均线（MA5, MA10, MA20, MA30, MA60）
- 成交量指标（成交量均线、成交量比率）
- 振幅指标（5日振幅、10日振幅）
- 支撑压力指标（量价支撑压力分析）
- 动量指标（突破、回踩）

### 3. 选股策略
- **选股策略一**：基于多维度评分的选股系统
  - 趋势维度（30分）：均线趋势和价格位置
  - 量能维度（30分）：成交额排名和成交量稳定性
  - 压力支撑维度（30分）：量价压力和支撑强度
  - 动量维度（20分）：突破趋势和回踩支撑

### 4. 交易策略
- 支持多种交易策略组合
- 仓位管理和风险控制
- 回测功能和绩效分析

## 📋 使用流程

### 环境准备
```bash
# 安装依赖
pip3 install -r requirements.txt

# 初始化数据库（首次使用）
python3 scripts/init_db.py
```

### 完整使用流程

#### 1. 下载历史数据
```bash
python3 scripts/download_efinance_history.py --market sh --start_date 20250530 --end_date 20250604
python3 scripts/download_efinance_history.py --market sz --start_date 20250530 --end_date 20250604
```

#### 2. 计算技术指标
```bash
python3 scripts/update_technical_indicators.py --start_date 20250530 --end_date 20250604
```

#### 3. 实时选股（开盘时间使用）
```bash
# 获取实时数据并粗选
python3 realtime_stock_picker.py --mode select

# 更新技术指标
python3 scripts/update_technical_indicators.py --start_date 20250604 --end_date 20250604

# 执行精选策略
python3 run_select_strategy.py
```

#### 4. 交易策略回测
```bash
python3 run_stock_strategy.py
```

## 🔧 核心配置

### 数据库配置
- 数据库文件：`stock_data.db`
- 自动备份：系统会自动创建备份文件
- 连接管理：通过`strategies/utils/db_utils.py`统一管理

### 选股条件配置
选股策略的主要筛选条件：
- 成交额 > 3亿
- 价格范围：5-200元
- 振幅控制：5日振幅 < 15%
- 技术指标：MA5 > MA10，价格 > MA5
- 成交量：在5日均量的0.5-2倍之间
- 排除ST股票和新股

## 📊 输出说明

### 选股结果输出
系统会输出包含以下信息的选股结果：
- 股票代码和名称
- 当前价格和成交额
- 各维度评分（趋势、量能、压支、动量）
- 综合总分
- 建议买入价格

### 日志记录
- 所有操作都会记录到`logs/`目录
- 支持按日期和功能分类的日志文件
- 错误和异常信息单独记录

## ⚠️ 注意事项

1. **数据完整性**：确保有足够的历史数据来计算技术指标（建议至少60个交易日）
2. **网络连接**：实时数据获取需要稳定的网络连接
3. **交易时间**：实时选股功能建议在交易时间使用
4. **风险提示**：本系统仅供学习和研究使用，投资有风险，决策需谨慎

## 🔄 系统维护

### 数据备份
系统已自动创建数据库备份：`stock_data_backup_20250604_201556.db`

### 日志清理
系统会自动清理3天前的旧日志文件

### 依赖更新
定期检查和更新Python依赖包：
```bash
pip3 install --upgrade -r requirements.txt
```
